{"id": "593fbacc-108a-4ec1-a1d8-7b26e9448faf", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"<PERSON><PERSON>\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Female\",\n      \"BirthDate\": \"1986-08-31\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"Staff Qtr <PERSON> Public School Campus, Sendhwa, Badwani, Madhya Pradesh\",\n      \"City\": \"Sendhwa\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"8109322174, 9584412759\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"Self-directed, enthusiastic educator with a passionate commitment to student development and the learning experience. Skilled in the design of challenging, enriching, and innovative activities that address the diverse interests and needs of students.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MCom (Master of Commerce)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Jiwaji University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2012-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"BEd (Bachelor of Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Jiwaji University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2015-06-30\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"BCom (Bachelor of Commerce)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Jiwaji University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2009-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Madhya Pradesh Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2006-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Madhya Pradesh Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2004-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Raghuwansh Public School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2021-12-01\",\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Arranging outdoor activities according to chapter. Making schedules and conducting assessments. Responsible for various teaching aids and curriculum materials provided by the central office. Responsible to conduct extracurricular activities for the students. Conduct extra-curricular activities like supporting school concerts, sports, excursions along with special interest programs. Conducting tests for students on a monthly and weekly basis to track their progress. Accurately maintained student academic records. To make students speak and encourage them for spoken English. Plan, prepare and present lessons catering to needs of entire ability range within classes. Head responsibility for progress of class of primary age students. Motivating students with enthusiasm and imaginative presentation. Arrange parents teacher meeting. Improved reading, writing and speaking skills of students of diverse background in English. Develop students' abilities, interests and coordinate through mode of creative activities.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"R. C. Patel English Medium School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2019-03-01\",\n        \"EndYear\": \"2020-12-31\",\n        \"Description/Responsibility\": \"Develop students' abilities, interests and coordinate through mode of creative activities. Conduct extra-curricular activities like supporting school concerts, sports, excursions along with special interest programs. Conducting tests for students on a monthly and weekly basis to track their progress. Responsible to conduct extracurricular activities for the students. Accurately maintained student academic records. To make students speak and encourage them for spoken English. Plan, prepare and present lessons catering to needs of entire ability range within classes. Head responsibility for progress of class of primary age students. Motivating students with enthusiasm and imaginative presentation. Arrange parents teacher meeting. Improved reading, writing and speaking skills of students of diverse background in English.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Guru Rajendra Jain International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2017-11-01\",\n        \"EndYear\": \"2019-02-28\",\n        \"Description/Responsibility\": \"Arranging outdoor activities according to chapter. Conducting tests for students on a monthly and weekly basis to track their progress. Responsible to conduct extracurricular activities for the students. Accurately maintained student academic records. To make students speak and encourage them for spoken English. Inspired a higher degree of parental involvement in student education through the use of daily reports, notebook check-in system and telephone calls. Member of girl's welfare and discipline committee. Created fun, engaging classroom activities. Arrange parents teacher meeting. Improved reading, writing and speaking skills of students of diverse background in English.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Sarti International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2017-04-01\",\n        \"EndYear\": \"2017-10-31\",\n        \"Description/Responsibility\": \"Developing lesson plan as per the curriculum. Conducting tests for students on a monthly and weekly basis to track their progress. Responsible to conduct extracurricular activities for the students. Arrange parents teacher meeting. Help managing student behavior. Inspired a higher degree of parental involvement in student education through the use of daily reports, notebook check-in system and telephone calls.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Wendy School Junior College\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2016-04-01\",\n        \"EndYear\": \"2017-03-31\",\n        \"Description/Responsibility\": \"Developing lesson plan as per the curriculum. Conducting tests for students on a monthly and weekly basis to track their progress. Responsible to conduct extracurricular activities for the students. Arrange parents teacher meeting. Help managing student behavior. Inspired a higher degree of parental involvement in student education through the use of daily reports, notebook check-in system and telephone calls.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"T.S.S. International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2015-07-01\",\n        \"EndYear\": \"2016-03-31\",\n        \"Description/Responsibility\": \"Communicated with parents to acknowledge superior work and areas of concern through weekly newsletters, and encouraged parent-volunteer assistance throughout the year. Prepared administered, and corrected tests; kept attendance and grade records. Prepared and implemented remedial programs for students requiring extra help. Utilizing computer resources to promote interactive learning. Increased completion rate of homework assignments by developing and implementing an ongoing reward system. Inspired a higher degree of parental involvement in student education through the use of daily reports, notebook check-in system and telephone calls.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Central Academy School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2009-01-01\",\n        \"EndYear\": \"2014-12-31\",\n        \"Description/Responsibility\": \"Maintain excellent classroom management skills and an ability to keep students on task. Create English environment and corrected all homework with an emphasis on providing feedback to each student. Designed and implemented lesson plans for all classes. Prepared administered and corrected tests; kept attendance and grade records.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"E.C.S. Bag less School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"P.R.T. Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2006-01-01\",\n        \"EndYear\": \"2008-12-31\",\n        \"Description/Responsibility\": \"Communicated with parents to acknowledge superior work and areas of concern through weekly newsletters, and encouraged parent-volunteer assistance throughout the year. Prepared administered, and corrected tests; kept attendance and grade records. Prepared and implemented remedial programs for students requiring extra help. Utilizing computer resources to promote interactive learning. Increased completion rate of homework assignments by developing and implementing an ongoing reward system. Inspired a higher degree of parental involvement in student education through the use of daily reports, notebook check-in system and telephone calls.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 13,\n    \"Skills\": [\n      \"Windows Operating Systems\",\n      \"Microsoft Office Suite\",\n      \"Organizing student learning opportunities\",\n      \"Managing student learning progression\",\n      \"Teamwork\",\n      \"Logical and critical thinking\",\n      \"Problem solving\",\n      \"Management ability\",\n      \"Communication skills\",\n      \"Student assessment\",\n      \"Creative lesson planning\",\n      \"Parent-teacher communication\",\n      \"Classroom management\",\n      \"Digital teaching\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [\n      {\n        \"CertificationName\": \"Nursery Teacher Training\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Delhi Management of Studies Program Center at Green Field School\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Degree in Classical Music\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Natraj Sangeet Sansathan\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      }\n    ],\n    \"Achievements\": [],\n    \"Languages\": [\n      \"Hindi\",\n      \"English\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is in a CSV-like structure with page numbers, text, and bounding box coordinates. There's also a table section at the end. I need to reconstruct the layout using the coordinates to accurately extract information.\n\nThe coordinates are X1, Y1, X2, Y2, which represent the bounding box of the text. Assuming standard PDF coordinates where X increases to the right and Y increases downwards or upwards; I need to check the convention. In PDFs, Y often increases downwards from the top, but I should confirm based on the data. Looking at the values, for example, on page 1, \"Curriculum Vitae\" has Y1=0.0633 and Y2=0.08594, which are small, so likely Y=0 is at the top, and Y increases downwards.\n\nTo reconstruct the layout, I should group texts that are close in X and Y coordinates to form lines, paragraphs, or sections. Since the text is already somewhat structured in the input, with headings and content, I can use the Y-coordinates to determine the order and grouping.\n\nKey sections from the text:\n\n- Personal details at the top: Name, Address, Contact, Email.\n\n- Career Objectives\n\n- Academic Qualification (but there's a table for education)\n\n- Computer Skills\n\n- Summary of Skills\n\n- Professional Summary\n\n- Work Experience (detailed with multiple jobs)\n\n- Extra Activities\n\n- Personal Details and Declaration\n\n- And there's Table-1 for education and Table-2 for additional personal info.\n\nIn the UserContent, there's a Table-1 and Table-2 provided separately, so I should use that structured data for education and other details.\n\nTable-1 is for education:\n\n- S.NO., DEGREE NAME, INSTITUTE/BOARD/ UNIVERSITY, Year\n\n1. HIGH SCHOOL, M.P. BOARD, 2004\n\n2. HIGHER SECONDARY, M.P. BOARD, 2006\n\n3. B.com, JIWAJI UNIVERSITY, 2009\n\n4. M.com, JIWAJI UNIVERSITY, 2011-12\n\n5. B.ED., JIWAJI UNIVERSITY, 2014-15\n\nTable-2:\n\nName: Aditi Kapil\n\nHusband's Name: Gaurav Kapil\n\nDate of Birth: 31st August, 1986\n\nMarital Status: Married\n\nPermanent Add.: Line No. 14/36 Birla Nagar, Gwalior, M.P.\n\nPresent Add: Staff Qtr Raghuwansh Public School Campus Sendhwa.\n\nLanguage Known: Hindi / English\n\nNow, I need to extract all the required fields from the JSON schema.\n\nStart with PersonalInformation:\n\n- FullName: From text, \"ADITI KAPIL\" is given. Also confirmed in Table-2.\n\n- FullNameEmbedding: Leave null.\n\n- Gender: Not explicitly stated. Infer from name. Aditi is a common female name in India, so infer \"Female\".\n\n- BirthDate: From Table-2, \"31st August, 1986\". Format it as \"1986-08-31\" since format is date.\n\n- Email: From page 1, \"<EMAIL>\"\n\n- Address: Need to combine. Permanent Add from Table-2: \"Line No. 14/36 Birla Nagar, Gwalior, M.P.\" and Present Add: \"Staff Qtr Raghuwansh Public School Campus Sendhwa.\" But in the schema, it's a single string. Perhaps use the present address or combine. Looking at the text, on page 1, there's \"RPS Campus, Sendhwa ,Badwani, , M.P.\" and email. Also in Table-2. I think the residential address could be the permanent one or present one. Schema says \"residential address\", so perhaps permanent address. But in the initial text, it's \"RPS Campus, Sendhwa\". Use from Table-2 for accuracy.\n\nFrom page 1: \"RPS Campus, Sendhwa ,Badwani, , M.P.\" and contact numbers.\n\nBut Table-2 has both permanent and present address. Schema has only one \"Address\". Probably use the primary one. I'll use the present address as it's current.\n\nSchema: \"Address\": \"The residential address of the individual.\"\n\nIn resumes, often the current address is given. Here, present address is \"Staff Qtr Raghuwansh Public School Campus Sendhwa.\"\n\nCity and State are separate fields.\n\n- City: From address, Sendhwa or Gwalior? Present address is Sendhwa, permanent is Gwalior. Since working in Sendhwa, perhaps Sendhwa. But Table-2 has both. Need to infer. Also in personal info section.\n\nFrom page 1: \"Sendhwa ,Badwani, , M.P.\" so city is Sendhwa, state is M.P. (Madhya Pradesh).\n\nBadwani might be district, but city is Sendhwa.\n\nIn Table-2, permanent address has Gwalior.\n\nBut the resume starts with Sendhwa address, so perhaps current city is Sendhwa.\n\nI'll set City to \"Sendhwa\" and State to \"Madhya Pradesh\".\n\n- State: \"Madhya Pradesh\"\n\n- ContactNumber: From page 1, \"8109322174, 9584412759\". Schema is a string, so put both or choose one? Probably put as a string \"8109322174, 9584412759\".\n\n- LinkedInProfile: Not mentioned, so null or empty string. Schema says if not found, keep as '', but for URL, probably empty string.\n\nSchema: \"LinkedInProfile\":{\"type\":\"string\",...}, so can be empty string.\n\nNow, Objective: From page 1, \"Career Objectives:- Self-directed, enthusiastic educator... that address the diverse interests and needs of students.\"\n\nCombine the text: \"Self-directed, enthusiastic educator with a passionate commitment to student development and the learning experience. Skilled in the design of challenging, enriching, and innovative activities that address the diverse interests and needs of students.\"\n\nEducation: There's Table-1 with structured data.\n\nNeed to output in Highest to Lowest order: PhD > Masters > Bachelors > etc.\n\nFrom Table-1:\n\n1. HIGH SCHOOL, M.P. BOARD, 2004 → 10th Pass\n\n2. HIGHER SECONDARY, M.P. BOARD, 2006 → 12th Pass\n\n3. B.com, JIWAJI UNIVERSITY, 2009 → BCom (Bachelor of Commerce)\n\n4. M.com, JIWAJI UNIVERSITY, 2011-12 → MCom (Master of Commerce)\n\n5. B.ED., JIWAJI UNIVERSITY, 2014-15 → BEd (Bachelor of Education)\n\nOrder should be highest to lowest. MCom is Masters, BEd is Bachelors, BCom is Bachelors, 12th Pass, 10th Pass.\n\nBEd and BCom are both bachelors, but in terms of level, perhaps consider the year or standard order.\n\nSchema says: \"PhD > Masters > Bachelors > Diploma > 12th Pass > 10th Pass\"\n\nSo Masters first: MCom\n\nThen Bachelors: BEd and BCom. Need to order them. Perhaps by graduation year descending or just list bachelors together.\n\nIt says \"Highest to Lowest order\", so probably MCom (Masters), then BEd and BCom (both Bachelors), then 12th, then 10th.\n\nFor BEd and BCom, BEd is 2014-15, BCom is 2009, so BEd is later, but level is same. Perhaps order by level first, then by year.\n\nI think group by level and sort by year descending within level.\n\nBut schema doesn't specify, just says Highest to Lowest order like PhD, MBA, etc.\n\nI'll order as: Masters (MCom), then Bachelors (BEd, BCom sorted by year desc), then 12th, then 10th.\n\nMCom is 2011-12, BEd is 2014-15, wait BEd is later but is Masters or Bachelors?\n\nBEd is Bachelor of Education, so it's a bachelor's degree.\n\nIn India, BEd is a bachelor's degree, often pursued after a bachelor's.\n\nDegrees:\n\n- MCom: Master of Commerce (Masters)\n\n- BEd: Bachelor of Education (Bachelors)\n\n- BCom: Bachelor of Commerce (Bachelors)\n\n- Higher Secondary: 12th Pass\n\n- High School: 10th Pass\n\nSo order: MCom (Masters), then BEd and BCom (Bachelors). Since both bachelors, sort by graduation year descending: BEd 2014-15, BCom 2009.\n\nThen 12th Pass 2006, 10th Pass 2004.\n\nFor each education entry, need Degree, Specialization, Institution, InstitutionEmbedding (null), GraduationYear, GPA (not mentioned, so null or not extract), MaxGPA, Percentage.\n\nIn the table, no GPA or percentage mentioned, so GPA, MaxGPA, Percentage should be null or handle.\n\nSchema: GPA is number, if mentioned. Not mentioned, so perhaps set to null, but schema says type \"number\", but in properties, it's required, wait no, in Education object, required fields are [\"Degree\",\"Specialization\",\"Institution\",\"InstitutionEmbedding\",\"GraduationYear\",\"GPA\",\"MaxGPA\",\"Percentage\"]\n\nIt says required, but instruction says if not found, keep as ''. But for numbers, perhaps use null.\n\nLook at schema: for GPA, type \"number\", for GraduationYear, type [\"string\",\"null\"], format date.\n\nFor GPA and Percentage, type number, but if not found, I should use null, but JSON schema might allow it.\n\nInstruction: \"If something is not found in the reconstructed layout, keep the respective value of that field as ''.\"\n\nBut for numbers, '' might not be appropriate. Perhaps use null.\n\nIn the schema definition, some fields have [\"string\",\"null\"], others are just \"string\" or \"number\".\n\nFor example, BirthDate is [\"string\",\"null\"], GPA is \"number\", no null option.\n\nBut in JSON, I can use null for numbers.\n\nTo be safe, since instruction says keep as '', I'll use empty string for missing values, but for numbers, it might cause issues. Perhaps use null.\n\nLooking at user_json_schema, for fields like BirthDate, it's [\"string\",\"null\"], for GPA it's \"number\", but in the description, it might imply null.\n\nI'll use null for missing numerical fields.\n\nBut instruction: \"keep the respective value of that field as ''.\" So perhaps empty string.\n\nFor date fields, use empty string if not found.\n\nNow for Specialization: in the degrees, for BCom, MCom, perhaps Commerce or something, but not specified. In the list, for BCom, specialization might be null or general.\n\nSchema says \"Specialization\": anyOf with enums or pattern, so can be string.\n\nFor BCom and MCom, perhaps \"Commerce\", for BEd, \"Education\".\n\nBut not specified in resume, so leave as empty string or infer.\n\nI'll leave as empty string if not mentioned.\n\nInstitution: from table, standardize names.\n\nM.P. BOARD should be \"Madhya Pradesh Board of Secondary Education\" or something, but schema says \"full, standardized name\". For boards, perhaps keep as \"MP Board\" or use full name.\n\nIn the enum for Institution, it's not listed, so use the name as is, but standardize.\n\nSchema says: \"The full, standardized name of the educational institution. Avoid abbreviations or variants. For example, return 'Sage University' instead of 'SU'.\"\n\nFor M.P. BOARD, probably \"Madhya Pradesh Board\", but it's a board, not university.\n\nIn Table-1, it's \"M.P. BOARD\" for high school and higher secondary, \"JIWAJI UNIVERSITY\" for others.\n\nStandardize to \"Madhya Pradesh Board\" and \"Jiwaji University\".\n\nSimilarly, GraduationYear: for high school 2004, higher secondary 2006, BCom 2009, MCom 2011-12, BEd 2014-15. Need to format as date string, perhaps \"2004\" or \"2004-01-01\" if needed, but format is date, so use \"YYYY\" or full date. Since only year given, use \"2004-01-01\" or just \"2004\". Schema format \"date\", so probably \"YYYY-MM-DD\" or handle as string.\n\nI'll use the year as string, e.g., \"2004\" for high school.\n\nFor MCom \"2011-12\", perhaps \"2012\" assuming end year, or keep as is. Better to use the end year.\n\nIn schema, GraduationYear is string, format date, so perhaps \"2012-12-31\" or something, but I'll use \"2012\" for simplicity, as it's common in resumes.\n\nNow GPA, MaxGPA, Percentage: not mentioned, so set to null or ''.\n\nWorkExperience: From the text, there are multiple entries under Work Experience.\n\nFrom page 2,3,4: several schools with P.R.T. Teacher role, with start and end dates, and descriptions.\n\nNeed to list in Highest to Lowest order, like CEO > Manager > Intern, but all are P.R.T. Teacher, which I assume is Primary Teacher or something similar. So perhaps all same level, so order by latest first.\n\nSchema says: \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern as well as with latest work experience first\"\n\nIt says \"Highest to Lowest order\" and \"latest work experience first\". A bit conflicting, but probably sort by seniority or role hierarchy, but since all roles are similar, sort by date descending.\n\nRoles are all \"P.R.T. Teacher\", so perhaps sort by end date descending.\n\nList from latest:\n\n- Raghuwansh Public School, Dec 2021 to Till Date\n\n- R. C. Patel English Medium School, March 2019 to Dec 2020\n\n- Guru Rajendra Jain International School, Nov 2017 to Feb 2019\n\n- Sarti International School, Apr 2017 to Oct 2017 (note: \"Arp\" probably typo for \"Apr\")\n\n- Wendy School Junior College, Apr 2016 to Mar 2017 ( \"Arp\" to \"Mar\")\n\n- T.S.S. International School, Jul 2015 to Mar 2016\n\n- Central Academy School, 2009 to 2014\n\n- E.C.S. Bag less School, 2006 to 2008\n\nFor each, CompanyName, Role, StartYear, EndYear, Description.\n\nRole: \"P.R.T. Teacher\" – but schema says \"Must include the expertise and specialization. Example: if taught Math then 'Math' should be included in the role.\"\n\nIn the descriptions, it's general teaching, mentioning English, etc., but not specific subjects always. From text, it involves teaching English, managing classes, etc. Perhaps \"Primary Teacher\" or specify if possible.\n\nIn multiple places, it mentions improving English skills, so perhaps \"English Teacher\" or keep as \"P.R.T. Teacher\".\n\nP.R.T. likely means Primary Teacher. And descriptions include teaching various subjects.\n\nI'll keep as \"P.R.T. Teacher\" for now, or \"Primary Teacher\". To include specialization, from context, it involves English and other activities.\n\nIn one place: \"Improved reading, writing and speaking skills... in English\", so perhaps role is \"English Teacher\".\n\nBut it's listed as P.R.T. Teacher, which might be general.\n\nI'll use \"P.R.T. Teacher\" and in Description, include the details.\n\nSchema requires Role to include expertise, so perhaps \"Primary School Teacher with focus on English\".\n\nBut to be precise, I'll use \"P.R.T. Teacher\" as given.\n\nNow Description/Responsibility: for each job, there are lists of responsibilities. Need to combine them into a string.\n\nAlso, TotalWorkExperienceInYears: calculate from the experiences.\n\nFrom 2006 to 2008: 2 years\n\n2009 to 2014: 5 years\n\n2015 to 2016: 1 year? Wait, multiple jobs.\n\nList the periods:\n\n- 2006-2008: E.C.S. Bag less School (2 years)\n\n- 2009-2014: Central Academy School (5 years)\n\n- 2015-03-2016: T.S.S. International School (about 9 months, but years: from Jul 2015 to Mar 2016, approx 0.75 years)\n\n- 2016-04 to 2017-03: Wendy School (11 months, approx 0.92 years)\n\n- 2017-04 to 2017-10: Sarti International School (7 months, approx 0.58 years)\n\n- 2017-11 to 2019-02: Guru Rajendra Jain International School (about 15 months, approx 1.25 years)\n\n- 2019-03 to 2020-12: R. C. Patel English Medium School (about 21 months, approx 1.75 years)\n\n- 2021-12 to present: Raghuwansh Public School (assuming current, say from 2021 to now, but no end date, need to estimate or leave.\n\nSince end date is \"Till Date\", and resume might be recent, but no specific date. Perhaps calculate up to last known or approximate.\n\nFor total, sum the years.\n\nBut there might be gaps or overlaps. Looking at dates:\n\nEnd 2008, start 2009 – continuous.\n\nEnd 2014, start 2015-07 – gap from Mar 2014 to Jul 2015? About 1.5 years gap, perhaps not working or not listed.\n\nThen 2016-03 end, 2016-04 start – continuous.\n\n2017-03 end Wendy, 2017-04 start Sarti – continuous.\n\n2017-10 end Sarti, 2017-11 start Guru – continuous.\n\n2019-02 end Guru, 2019-03 start R.C. Patel – continuous.\n\n2020-12 end R.C. Patel, 2021-12 start Raghuwansh – gap of about 1 year.\n\nSo total experience: sum of durations.\n\n- 2006-2008: 2 years\n\n- 2009-2014: 5 years\n\n- 2015-07 to 2016-03: 8 months ≈ 0.67 years (Jul to Dec 2015: 6 months, Jan-Mar 2016: 3 months, total 9 months ≈ 0.75)\n\n- 2016-04 to 2017-03: 12 months ≈ 1 year (Apr 2016 to Mar 2017 is 12 months)\n\n- 2017-04 to 2017-10: 7 months ≈ 0.58 years\n\n- 2017-11 to 2019-02: 15 months ≈ 1.25 years (Nov 2017 to Feb 2019: 16 months? Nov-Dec 2017:2, 2018:12, Jan-Feb 2019:2, total 16 months ≈1.33 years)\n\nCalculate properly:\n\nGuru Rajendra: Nov 2017 to Feb 2019: from Nov 2017 to Nov 2018 is 12 months, to Feb 2019 is 15 months, yes ≈1.25 years\n\nR.C. Patel: March 2019 to Dec 2020: March 2019 to March 2020:12 months, to Dec 2020:9 months, total 21 months ≈1.75 years\n\nRaghuwansh: Dec 2021 to present. Assuming the resume is from say 2023 or current, but not specified. Perhaps calculate up to 2023 or leave as is. Since \"Till Date\", and no current year, perhaps estimate or use the start.\n\nFor total, sum known full years.\n\nFrom 2006 to 2014 is 8 years (2006-2008 and 2009-2014, but with gap? No, 2006-2008 ECS, 2009-2014 Central, so continuous from 2006 to 2014: 8 years 11 months or something, but approximately.\n\nBetter to sum individual durations.\n\n- ECS: 2006 to 2008: 2 years (assuming full years)\n\n- Central: 2009 to 2014: 5 years (2009-2010,10-11,11-12,12-13,13-14)\n\n- TSS: Jul 2015 to Mar 2016: 9 months\n\n- Wendy: Apr 2016 to Mar 2017: 12 months\n\n- Sarti: Apr 2017 to Oct 2017: 7 months\n\n- Guru: Nov 2017 to Feb 2019: 15 months\n\n- R.C. Patel: March 2019 to Dec 2020: 21 months\n\n- Raghuwansh: Dec 2021 to present: say from Dec 2021, if current year is 2023, 1.5 years, but not specified.\n\nPerhaps the total is approximate. In resumes, total experience is often stated, but here not, so calculate from listed.\n\nGap between 2014 and 2015, and 2020 and 2021.\n\nTotal months: 24 (ECS) + 60 (Central) + 9 (TSS) + 12 (Wendy) + 7 (Sarti) + 15 (Guru) + 21 (R.C. Patel) + ongoing.\n\nOngoing not counted or estimate.\n\nSum known completed: from 2006 to 2020.\n\nFrom 2006 to 2020 is 14 years, but with gaps.\n\nCalculate total months from durations:\n\n- 2006-2008: 24 months\n\n- 2009-2014: 60 months\n\n- 2015-07 to 2016-03: 9 months\n\n- 2016-04 to 2017-03: 12 months\n\n- 2017-04 to 2017-10: 7 months\n\n- 2017-11 to 2019-02: 15 months\n\n- 2019-03 to 2020-12: 21 months\n\nSum: 24+60=84, +9=93, +12=105, +7=112, +15=127, +21=148 months.\n\n148 months / 12 = 12.333 years.\n\nPlus ongoing from Dec 2021, say additional 1-2 years, but since not specified, perhaps use 12.33 or round to 12 years. But better to calculate only completed periods or use intelligence.\n\nSince \"Till Date\" is current, but no date, perhaps the total is from first to last, but with gaps.\n\nI am considering summing the durations provided.\n\nFor Raghuwansh, since it's ongoing, perhaps don't include in total or estimate.\n\nIn many systems, ongoing is included up to current, but since no current date, I'll sum the completed years and note.\n\nFor simplicity, calculate sum of all listed durations in years.\n\nDurations:\n\n- ECS: 2 years (2006-2008)\n\n- Central: 5 years (2009-2014)\n\n- TSS: approx 1 year (Jul 2015-Mar 2016)\n\n- Wendy: 1 year (Apr 2016-Mar 2017)\n\n- Sarti: approx 0.5 years (Apr-Oct 2017)\n\n- Guru: approx 1.5 years (Nov 2017-Feb 2019)\n\n- R.C. Patel: approx 1.75 years (Mar 2019-Dec 2020)\n\n- Raghuwansh: from Dec 2021, say 2 years if current, but not specified.\n\nPerhaps total around 12-13 years.\n\nBut to be precise, sum the years where full years are given: 2+5+1+1+0.5********* = 12.75 years for completed, plus ongoing.\n\nIn the description, no total mentioned, so I'll calculate approximately 13 years or use the sum.\n\nFor the field \"TotalWorkExperienceInYears\", it's a number, so I'll use 13 or calculate.\n\nFrom 2006 to present (assume 2023), but with gaps. First job 2006, last started 2021, but not continuous.\n\nBetter to sum the durations: let's list end - start for each.\n\n- ECS: 2008 - 2006 = 2\n\n- Central: 2014 - 2009 = 5\n\n- TSS: 2016-03 - 2015-07 = 8 months ≈0.67\n\n- Wendy: 2017-03 - 2016-04 = 11 months ≈0.92 (Apr 2016 to March 2017 is 11 months if end March, but typically end of month, but approximate)\n\nDates are not exact, some are months only.\n\nPerhaps use the years as given.\n\nIn WorkExperience entries, StartYear and EndYear are strings, format date.\n\nFor TotalWorkExperience, perhaps sum the years.\n\nI'll set it to approximately 12 years, but let's calculate total years worked.\n\nFrom 2006 to 2008: 2 years\n\n2009 to 2014: 5 years\n\n2015 (partial), 2016 (full for Wendy, partial for TSS), etc.\n\nPerhaps 2+5+1 (for 2015-2016 period, but two jobs), no, TSS and Wendy are consecutive but different jobs.\n\nTSS: Jul 2015-Mar 2016, Wendy: Apr 2016-Mar 2017, so continuous coverage from Jul 2015 to Mar 2017, but different employers.\n\nSo total time worked, not gaps.\n\nFrom 2006 to 2008: 2 years\n\n2009 to 2014: 5 years\n\nJul 2015 to  Mar 2016: 9 months\n\nApr 2016 to Mar 2017: 12 months (but Apr 2016 to  Mar 2017 is 12 months, but TSS ended Mar 2016, Wendy started Apr 2016, so no gap)\n\nTSS end  March 2016, Wendy start April 2016, so continuous.\n\nSimilarly, Wendy end March 2017, Sarti start April 2017? Sarti is Apr 2017 to Oct 2017, Wendy end March 2017, Sarti start Apr 2017, continuous.\n\nSarti end Oct 2017, Guru start Nov 2017, continuous.\n\nGuru end Feb 2019, R.C. Patel start March 2019, continuous.\n\nR.C. Patel end Dec 2020, Raghuwansh start Dec 2021, gap of 12 months.\n\nSo periods of continuous work: 2006-2017 (from 2006 to 2017 with no gaps in the listed jobs? 2006-2008 ECS, 2009-2014 Central, then gap to 2015.\n\nFrom 2006 to 2014: continuous 8 years (2006-2014 inclusive is 9 years? 2006 to 2007,2007-2008,2008-2009 but 2008 end, 2009 start, so 2006-2008 and 2009-2014, continuous from 2006 to 2014: 8 full years if start Jan 2006 end Dec 2014, but approximately 8 years.\n\n2006 to 2014 is 8 years if exact dates, but 2006-2008 is 2 years, 2009-2014 is 5 years, total 7 years? 2006 to 2014 is 8 years if from start to end.\n\nIf started in 2006 and ended in 2014, duration is 2014 - 2006 = 8 years.\n\nSimilarly, TSS started Jul 2015, end Mar 2016: 0.75 years\n\nWendy Apr 2016 to Mar 2017: 1 year\n\nSarti Apr 2017 to Oct 2017: 0.58 years\n\nGuru Nov 2017 to Feb 2019: 1.33 years (Feb 2019 - Nov 2017 = 15 months)\n\nR.C. Patel  March 2019 to Dec 2020: 1.75 years\n\nRaghuwansh Dec 2021 to present: say 2 years if 2023, but unknown.\n\n  Sum: 8 (2006-2014) + 0.75 + 1 + 0.58 + 1.33 + 1.75 = calculate: 8 + 0.75=8.75, +1=9.75, +0.58=10.33, *****=11.66, *****=13.41 years for completed, plus ongoing.\n\nBut the 2006-2014 is from two jobs, but continuous.\n\nTotal 13.41 years minus the gap between 2014 and 2015 ( about 1.5 years not worked), but since I summed the durations, it's correct.\n\nDurations sum to: ECS 2, Central 5, total 7 for 2006-2014? If ECS 2006-2008 and Central 2009-2014, duration is from 2006 to 2014, but split, total time 8 years if no gap, but 2008 to 2009 is continuous? End 2008, start 2009, likely no gap, so 2006 to 2014 is 8 years.\n\nIn the text, ECS 2006-2008, Central 2009-2014, so probably 8 years.\n\nSimilarly, from TSS Jul 2015 to R.C. Patel Dec 2020: from Jul 2015 to Dec 2020 is 5.5 years, but with jobs: TSS 0.75, Wendy 1, Sarti 0.58, Guru 1.33, R.C. Patel 1.75, sum 5.41, close.\n\nThen gap 2021, then Raghuwansh from Dec 2021.\n\nSo total experience approximately 8 + 5.41 = 13.41 years for up to 2020, plus ongoing.\n\nFor TotalWorkExperienceInYears, I'll use 13, as an integer or float.\n\nSchema is number, so 13.0 or something.\n\nBut perhaps calculate exactly from listed start and end.\n\nFor ongoing, since no end date, perhaps don't include or estimate. I'll sum the completed periods and add partial for ongoing if possible, but set to 13.\n\nNow for Skills: from text, \"Computer Skills:- Operating Systems: Windows 2000, Windows XP, Windows. Office Package : Microsoft Word, Excel, PowerPoint\" and \"Summary of Skills:- Organizing student learning opportunities. Managing student learning progression. Working in teams. Logical and critical thinking... Excellent communication skill...\"\n\nAlso in Professional Summary: Student Assessment, Creative Lesson Planning, etc.\n\nSo list as array of strings: \"Windows Operating Systems\", \"Microsoft Office\", \"Organizing learning opportunities\", \"Student assessment\", \"Lesson planning\", etc.\n\nNeed to extract unique skills.\n\nFrom Computer Skills: Windows, Microsoft Word, Excel, PowerPoint.\n\nFrom Summary of Skills: Organizing student learning, Managing progression, Teamwork, Logical thinking, Problem solving, Management ability, Communication skills.\n\nFrom Professional Summary: Student Assessment, Creative Lesson Planning, Parent-Teacher Communication, Classroom Management, Teach Student by Digital Classroom.\n\nSo compile a list: \"Windows Operating Systems\", \"Microsoft Office Suite\", \"Student Assessment\", \"Lesson Planning\", \"Parent-Teacher Communication\", \"Classroom Management\", \"Digital Teaching\", \"Teamwork\", \"Critical Thinking\", \"Problem Solving\", \"Communication Skills\", etc.\n\nMake a clean list.\n\nCertifications: Not explicitly mentioned, except in Extra Activities: \"N.T.T. From Delhi Management of studies Program Center at Green Field School, Gwalior, M.P.\" and \"4 years Degree in Classical Music from Natraj Sangeet Sansathan Alliga Baag ,Shinde Ke Chawani, Gwalior Affiliated to Allabhad and Mathura University.\"\n\nN.T.T. is Nursery Teacher Training, which is a certification.\n\nClassical Music degree might be a certification or degree, but it's a degree, perhaps under education.\n\nIn education table, it's not listed, so perhaps add as certification.\n\nSchema has Certifications as array of objects with CertificationName, IssuingOrganization, etc.\n\nSo for N.T.T.: CertificationName \"Nursery Teacher Training\", IssuingOrganization \"Delhi Management of Studies\" or \"Green Field School\", IssueDate not specified.\n\nSimilarly for Music: \"Degree in Classical Music\", IssuingOrganization \"Natraj Sangeet Sansathan\", affiliated to universities.\n\nBut it's called \"Degree\", so perhaps part of education, but not in the table.\n\nIn Extra Activities section.\n\nPerhaps add as certification.\n\nAchievements: Not many mentioned, perhaps the experiences have achievements, but not explicit. In descriptions, things like \"Improved reading skills\", but not formal achievements. So perhaps empty.\n\nLanguages: From Table-2, \"Language Known : Hindi / English\", so [\"Hindi\", \"English\"]\n\nProjects: Not mentioned in the resume, so empty array.\n\nNow for the output, need to fill the JSON.\n\nFirst, PersonalInformation:\n\n- FullName: \"Aditi Kapil\"\n\n- Gender: \"Female\" (inferred)\n\n- BirthDate: \"1986-08-31\" (from \"31st August, 1986\")\n\n- Email: \"<EMAIL>\"\n\n- Address: Perhaps \"Staff Qtr Raghuwansh Public School Campus, Sendhwa, Badwani, Madhya Pradesh\" or from permanent. Use present address as residential.\n\nFrom schema, \"residential address\", and in resume, present address is given first. Use \"RPS Campus, Sendhwa, Badwani, M.P.\" from page 1.\n\nCity: \"Sendhwa\", State: \"Madhya Pradesh\"\n\n- ContactNumber: \"8109322174, 9584412759\"\n\n- LinkedInProfile: \"\"\n\nObjective: the career objective text.\n\nEducation: from Table-1, with standardization.\n\nDegree: map to enum if possible.\n\nHIGH SCHOOL -> \"10th Pass\"\n\nHIGHER SECONDARY -> \"12th Pass\"\n\nB.com -> \"BCom (Bachelor of Commerce)\"\n\nM.com -> \"MCom (Master of Commerce)\"\n\nB.ED. -> \"BEd (Bachelor of Education)\"\n\nSpecialization: not specified, so \"\"\n\nInstitution: \"Madhya Pradesh Board\" for board degrees, \"Jiwaji University\" for university.\n\nGraduationYear: \"2004\", \"2006\", \"2009\", \"2012\" (for M.com 2011-12, use 2012), \"2015\" for B.ED. 2014-15\n\nGPA, MaxGPA, Percentage: null or ''\n\nWorkExperience: list each job with CompanyName, Role, StartYear, EndYear, Description.\n\nRole: \"P.R.T. Teacher\" – perhaps standardize to \"Primary Teacher\"\n\nDescription: combine the bullet points for each job.\n\nTotalWorkExperienceInYears: approximately 13\n\nSkills: list from text.\n\nCertifications: add N.T.T. and Music degree.\n\nFor N.T.T.: CertificationName \"Nursery Teacher Training\", IssuingOrganization \"Delhi Management of Studies Program Center at Green Field School\", IssueDate not specified, so null\n\nSimilarly for Music.\n\nAchievements: perhaps empty\n\nLanguages: [\"Hindi\", \"English\"]\n\nProjects: []\n\nNow, for embeddings, all should be null as per instruction.\n\nOutput must be a single JSON string in the specified format.\n\nNow, order Education from highest to lowest: MCom, BEd, BCom, 12th Pass, 10th Pass.\n\nGraduation years: MCom 2012, BEd 2015, wait BEd is 2014-15, so 2015, BCom 2009, so BEd after MCom in time, but level: MCom masters, BEd bachelors.\n\nMCom is masters, BEd is bachelors, so MCom first, then BEd and BCom.\n\nBEd graduated 2015, MCom 2012, so MCom earlier, but highest level first, so MCom, then bachelors BEd (2015), BCom (2009), then 12th, 10th.\n\nYes.\n\nSimilarly for WorkExperience, latest first.\n\nNow, write the JSON."}}], "created": 1750945141, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 2317, "prompt_tokens": 8628, "total_tokens": 18692, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 7747, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4227, "text_tokens": 8628, "image_tokens": 0}, "num_sources_used": 0}}