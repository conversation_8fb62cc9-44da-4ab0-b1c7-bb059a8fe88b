{"id": "c2d1145d-5b49-466d-a490-049a43de2fd7", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"<PERSON><PERSON><PERSON>\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Male\",\n      \"BirthDate\": \"1993-03-01\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"407 Navlakha, Indore 452001\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"8827781708\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"To Contribute best of my knowledge, skill and ability in the growth of the organization where I will be working and attaining the new heights in my field work.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MA (Master of Arts)\",\n        \"Specialization\": \"English\",\n        \"Institution\": \"Rabindranath Tagore University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 80\n      },\n      {\n        \"Degree\": \"PGDCA (Post Graduate Diploma in Computer Applications)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Rabindranath Tagore University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 70\n      },\n      {\n        \"Degree\": \"BEd (Bachelor of Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Arihant College\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 68\n      },\n      {\n        \"Degree\": \"Post Graduation Diploma in Yogic therapy\",\n        \"Specialization\": \"Yogic therapy\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 70\n      },\n      {\n        \"Degree\": \"BE (Bachelor of Engineering)\",\n        \"Specialization\": \"Mechanical Engineering\",\n        \"Institution\": \"Chameli Devi Group of Institutions\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2015-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 66\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Kendriya Vidyalaya Dewas\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 60\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Kendriya Vidyalaya Dewas\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 61\n      },\n      {\n        \"Degree\": \"CTET (Central Teacher Eligibility Test)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Central Board of Secondary Education\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"MPTET (Madhya Pradesh Teacher Eligibility Test)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"IIT-JEE NEET Coaching Institute Dewas\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Centre Head\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Managed and led coaching center for IIT-JEE and NEET preparation.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Toppr app (Byjus)\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Chemistry Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Taught chemistry to students for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Gyan 360 Coaching\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Chemistry Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Provided chemistry education for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Modern International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Chemistry Faculty\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Taught chemistry as faculty for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Pooja Public School Indore\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Chemistry Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Instructed chemistry to students for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Rukhma Devi School Indore\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Science Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Taught science subjects for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Vidhyashree School Indore\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Chemistry Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Worked as chemistry teacher for six months.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Matushree Ahilyabai School Indore\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physics Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Taught physics for six months.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Modern International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physical Education Faculty\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Handled physical education duties for one year.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 7,\n    \"Skills\": [],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"Selected for Central Zone Cricket Tournament 2014\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Central Zone Cricket Association\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2014-01-01\"\n      },\n      {\n        \"AchievementName\": \"Selected for School Games Federation of India Cricket Tournament\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"School Games Federation of India\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"Selected for KVS National Cricket Tournament 2011\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Kendriya Vidyalaya Sangathan\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-01-01\"\n      },\n      {\n        \"AchievementName\": \"Selected for KVS National Cricket Tournament 2010\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Kendriya Vidyalaya Sangathan\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2010-01-01\"\n      },\n      {\n        \"AchievementName\": \"Selected for KVS National Cricket Tournament 2009\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Kendriya Vidyalaya Sangathan\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2009-01-01\"\n      },\n      {\n        \"AchievementName\": \"Selected for KVS National Cricket Tournament 2008 with Gold Medal\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Kendriya Vidyalaya Sangathan\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-01-01\"\n      }\n    ],\n    \"Languages\": [],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent which is a mix of text from a PDF conversion, including coordinates, and a table. I need to analyze this to reconstruct the layout using the bounding box coordinates (X1, Y1, X2, Y2) to understand the positioning.\n\nThe UserContent starts with page-by-page text with coordinates, and then there's a Table-1 with structured data. I must consider the relative locations to fetch information accurately.\n\nLet me list out the key sections from the text:\n\n- Page 1:\n\n  - RESUME at top\n\n  - Personal info: Akash <PERSON>, address, mobile numbers, email\n\n  - Career objective\n\n  - Academic records with various degrees and percentages\n\n- Page 2:\n\n  - Work Experience\n\n  - Achievements\n\n  - Personal Profile (mentioned in text)\n\n- Page 3:\n\n  - Declaration\n\n  - And the Table-1 which seems to have structured personal info like DO<PERSON>, <PERSON>'s name, etc.\n\nThe Table-1 is provided separately in the UserContent, so I need to integrate that into the layout. Since it's labeled as Table-1, it might be from a specific part of the document. Looking at the coordinates, the text on page 3 ends with \"Place: Indore\", and the table might be associated with that.\n\nBut in the UserContent, Table-1 is given after the page texts, so I should treat it as part of the content. The table has keys and values like Name, Date of Birth, etc.\n\nNow, for Step 2: Reconstruct the exact layout. I need to use X and Y coordinates to understand grouping. Typically, in PDFs, Y-coordinate decreases as we go down the page (top to bottom), and X increases left to right.\n\nFor example, on page 1:\n\n- RESUME: Y from 0.0744 to 0.10125 (high Y, so top)\n\n- Akash Gavlane address: Y around 0.25141 to 0.26469\n\n- Mobile no: Y 0.27619 to 0.28883 and 0.29958 to 0.31345\n\n- Email: Y 0.34099 to 0.35556\n\n- Career objective: Y 0.37635 to 0.39415 and below\n\n- Academic records start at Y 0.47697\n\nOn page 2, Work Experience at Y 0.10707, etc.\n\nThe table is separate, but I can assume it's on page 3 or integrated.\n\nIn the UserContent, after the page texts, there's \"Table-1\" with rows. I need to parse that.\n\nTable-1:\n\n- Name : Akash Gavlane\n\n- Date of Birth : 01/03/1993\n\n- Fathers Name : Shri Ashok kumar gavlane\n\n- Sex : Nationality : Male Indian\n\n- Married/Single: single\n\nThis seems like a structured section, probably from the resume.\n\nNow, for Step 3: Fill out the required output in the JSON schema.\n\nI need to extract all fields from the schema.\n\nStart with PersonalInformation:\n\n- FullName: From text, \"Akash Gavlane\" is mentioned multiple times. Also in Table-1. So, \"Akash Gavlane\"\n\n- FullNameEmbedding: Leave null as per instructions.\n\n- Gender: From Table-1, \"Sex : Nationality : Male Indian\" so Gender is \"Male\"\n\n- BirthDate: From Table-1, \"Date of Birth : 01/03/1993\" – I need to format it as date. Probably \"1993-03-01\" or keep as string. Schema says format \"date\", so I'll use \"1993-03-01\" assuming ISO format.\n\n- Email: From text, \"<EMAIL>.\" so \"<EMAIL>\"\n\n- Address: From text, \"Akash Gavlane 407 Navlakha, Indore 452001\" and mobile nos. So address is \"407 Navlakha, Indore 452001\"\n\n- City: From address, \"Indore\". Also mentioned in other places.\n\n- State: Not explicitly stated, but Indore is in Madhya Pradesh, India. Schema allows pattern \".*\" so I can put \"Madhya Pradesh\" based on knowledge.\n\nSchema for State has an enum including \"Madhya Pradesh\", yes.\n\n- ContactNumber: Multiple numbers given: \"Mobile no :-, 8827781708, 7987891708\". I should pick one or combine? Schema says \"The contact phone number.\" probably best to put the first one or all. But it's a string, so I can put \"8827781708\" or both. I'll put the primary one, say \"8827781708\".\n\n- LinkedInProfile: Not mentioned anywhere, so null or empty string. Schema says if not found, keep as '', but for strings, it's probably empty string.\n\nSchema doesn't specify null for strings, but for some fields it has [\"string\",\"null\"], but for ContactNumber and LinkedInProfile, it's \"type\":\"string\", so I should use empty string if not found.\n\nLooking back: ContactNumber and LinkedInProfile are \"type\":\"string\", no null option, so use empty string.\n\nIn PersonalInformation properties, all are required, but instructions say if not found, keep as ''. So I'll use empty strings.\n\nLinkedInProfile not mentioned, so ''.\n\nNow, Objective: From text, \"Career objective: To Contribute best of my knowledge, skill and ability in the growth of the organization, where I will be working and attaining the new heights in my field work.\" So, combine that into a string.\n\nEducation: Need to list in highest to lowest order. From Academic records:\n\n- MA English With 80% from Rabindranath Tagore University. (Running) – but it's running, so perhaps not completed. Schema has GraduationYear, so if not graduated, maybe not include or handle. But it's mentioned, so perhaps include with GraduationYear null.\n\nSchema required fields include GraduationYear, which can be null.\n\nList degrees:\n\n- Bachelor Of Engineering (Mech) 2015 with 66%\n\n- Senior Secondary (12th) from CBSE, K.V Dewas with 60%\n\n- High Secondary (10th) from CBSE, K.V Dewas with 61% – wait, High Secondary might be 10th, Senior Secondary 12th.\n\nText says:\n\n- Completed Senior Secondary From C.B.S.E Board From K.V Dewas With 60%. – probably 12th\n\n- Completed High Secondary From C.B.S.E Board From K.V Dewas With 61 %. – probably 10th, but typically High School is 10th, Senior Secondary is 12th. Might be a typo or regional term.\n\nIn India, Secondary is 10th, Senior Secondary is 12th.\n\n- Post Graduation Diploma in Yogic therapy with 70% from DAVV Indore\n\n- B.ED From Arihant College 68% Indore DAVV\n\n- CTET Qualified Paper I with 75% & Paper II with 65% – this is a test, not a degree. Schema has specific entries like \"CTET\" in the enum for Degree.\n\nLooking at schema, Degree anyOf has enum including \"CTET (Central Teacher Eligibility Test)\", etc.\n\nSo, I need to map:\n\n- 10th Pass: High Secondary with 61%, institution K.V Dewas, year not specified, but probably around 2011 or so, but not given. GraduationYear null?\n\nText doesn't specify years for all, only for B.E.\n\n- For 12th: no year\n\n- B.E: 2015\n\n- Others no years.\n\nAlso, MA English is running, so perhaps not include or set GraduationYear null.\n\nSchema required GraduationYear, which can be null.\n\nOrder: Highest to lowest: PhD > Masters > Bachelors > etc.\n\nHere:\n\n- MA English (running) – Masters level\n\n- B.ED – Bachelor level education\n\n- PGD in Yogic therapy – Diploma\n\n- B.E – Bachelor\n\n- 12th, 10th\n\nCTET, MPTET are certifications or tests, might fit better in Certifications section.\n\nSchema has Education with specific degrees, and Certifications separate.\n\nIn Education enum, there is \"CTET\", \"MPTET\", etc.\n\nSo, include them in Education.\n\nList all:\n\n1. MA English – Degree: \"MA (Master of Arts)\", Specialization: \"English\", Institution: \"Rabindranath Tagore University\", GraduationYear: null (running), GPA/Percentage: 80% mentioned, but it's running, so perhaps not set, but text says \"With 80%\", wait no, \"MA English With 80% from Rabindranath Tagore University. ( Running )\" – it says with 80%, but running, so maybe percentage is for something else or anticipated. I'll include it.\n\nPerhaps the 80% is for a previous exam or something, but I'll extract as is.\n\n2. PGDCA With 70% from Rabindranath Tagore University. – Degree: match to \"PGDCA (Post Graduate Diploma in Computer Applications)\"\n\n3. B.ED – \"BEd (Bachelor of Education)\"\n\n4. Post Graduation Diploma in Yogic therapy – not in enum, so use pattern \"Post Graduation Diploma in Yogic therapy\"\n\n5. Bachelor Of Engineering (Mech) – \"BTech (Bachelor of Technology)\" or \"BE (Bachelor of Engineering)\", Specialization: \"Mechanical Engineering\"\n\n6. Senior Secondary (12th) – \"12th Pass\"\n\n7. High Secondary (10th) – \"10th Pass\"\n\n8. CTET Qualified – \"CTET (Central Teacher Eligibility Test)\"\n\n9. MPTET Qualified – \"MPTET (Madhya Pradesh Teacher Eligibility Test)\"\n\nFor CTET and MPTET, no percentage or year given, except CTET has percentages in text: \"CTET Qualified Paper I with 75% & Paper II with 65%\"\n\nMPTET no percentage.\n\nInstitutions vary.\n\nGraduationYear: only B.E has 2015. Others not specified, so null.\n\nGPA and MaxGPA: not mentioned, only percentages. Schema says if GPA mentioned, extract, else percentage. Here percentages are given, so set Percentage, and GPA and MaxGPA can be null or inferred, but schema requires them, with type number, can be null? No, GPA and MaxGPA are \"type\":\"number\", not optional null, but instructions say if not found, keep as '', but for numbers, perhaps 0 or null. Schema in properties has no null, but in description, it might be handled.\n\nLooking back, in Education properties, GPA and MaxGPA are \"type\":\"number\", but in the array items, they are required. But instructions say if not found, keep as ''. So probably use null or empty, but JSON schema might not allow, but I can use null.\n\nIn JSON, I can use null for numbers.\n\nSimilarly for Percentage.\n\nBut Percentage can be calculated from GPA, but here percentage is given directly.\n\nFor each education entry, set Percentage if mentioned.\n\nMaxGPA: need to infer. For example, for university degrees, often 100% scale, so MaxGPA could be 100, but schema says \"use knowledge to infer GPA scale\". For percentages given, perhaps set MaxGPA to 100.\n\nGPA is not mentioned, only percentage, so perhaps set GPA to null, and Percentage to the value.\n\nSchema says: \"If mentioned, extract GPA value. ... Percentage: Convert GPA to percentage if GPA is given, else extract if mentioned.\"\n\nHere, GPA not mentioned, percentage is mentioned, so set Percentage directly, GPA and MaxGPA can be null.\n\nBut schema required them, so I'll set to null.\n\nNow order: Highest to lowest. MA is highest, then B.ED, PGDCA, BE, 12th, 10th, CTET, MPTET.\n\nCTET and MPTET are not degrees, but tests. In the enum, they are listed, so include in Education.\n\nPGD in Yogic therapy might be diploma level.\n\nStandard order: PhD > Masters > Bachelors > Diploma > 12th > 10th > Certifications like CTET.\n\nSchema says \"like PhD, MBA, MSc, BSc, 10th Pass, 12th Pass\" so probably group degrees.\n\nI'll order as:\n\n1. MA English (Masters)\n\n2. PGDCA (Post Grad Diploma)\n\n3. B.ED (Bachelor)\n\n4. BE (Bachelor)\n\n5. Post Grad Diploma in Yogic therapy (Diploma)\n\n6. Senior Secondary 12th\n\n7. High Secondary 10th\n\n8. CTET\n\n9. MPTET\n\nPGDCA and PGD Yogic therapy both diplomas, but PGDCA is in enum.\n\nInstitution names: need standardized. \"Rabindranath Tagore University\" – keep as is. \"Chameli Devi Group Of Institutions\" – perhaps standardize, but schema says \"full, standardized name\", avoid abbreviations. So \"Chameli Devi Group of Institutions\". \"K.V Dewas\" probably \"Kendriya Vidyalaya Dewas\", but text says \"K.V Dewas\", schema says avoid abbreviations, so perhaps \"Kendriya Vidyalaya Dewas\". Similarly, \"DAVV Indore\" is \"Devi Ahilya Vishwavidyalaya Indore\", but text has \"DAVV\", so use \"Devi Ahilya Vishwavidyalaya\" or keep as per text? Schema says \"full, standardized name\", so I should use full names if known.\n\nFor \"K.V Dewas\", I know it's Kendriya Vidyalaya, so \"Kendriya Vidyalaya Dewas\".\n\nFor \"DAVV Indore\", \"Devi Ahilya Vishwavidyalaya Indore\".\n\nFor \"Arihant College\", keep as \"Arihant College\".\n\nFor \"Rabindranath Tagore University\", keep as is.\n\nNow WorkExperience: List in highest to lowest role and latest first.\n\nRoles:\n\n- Centre Head of IIT-JEE|NEET Coaching Dewas – probably highest role\n\n- Chemistry Teacher at various places\n\nText:\n\n- Centre Head... (no duration specified, but listed first)\n\nDurations given for some:\n\n- 1 Year as Chemistry Teacher Toppr app\n\n- 1 Year Experience as Chemistry Gyan 360 coaching\n\n- 1 Year experience as Chemistry faculty at Modern International School\n\n- 1Year Experience as Chemistry teacher in pooja public school\n\n- 1Year Experience as Science teacher in Rukhma devi School\n\n- 6 Month Worked as Chemistry teacher Vidhyashree School\n\n- 6 Month Worked as Physics in Matushree Ahilyabai School\n\n- 1 Year experience as Physical Education faculty at Modern International (probably same school)\n\nOrder by latest first? Years not specified, but perhaps assume recent.\n\nCentre Head might be current or recent.\n\nRoles: Centre Head is highest, then teacher roles.\n\nSchema says \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern as well as with latest work experience first\"\n\nIt says \"Highest to Lowest order\" and \"latest first\", might be conflicting. Probably sort by date descending, and within, by seniority.\n\nDates not given, only durations like 1 year, but no specific years.\n\nFrom the list, perhaps Centre Head is most recent.\n\nI'll list with latest assumed first. Perhaps Centre Head, then the 1-year experiences.\n\nFor each, CompanyName, Role, StartYear, EndYear – not specified, so null.\n\nDescription/Responsibility: need to extract. For example, for Centre Head, no description, similarly for others.\n\nPerhaps use the role text as description.\n\nSchema has \"Description/Responsibility\", so I can put a brief from the text.\n\nTotalWorkExperienceInYears: sum of experiences. Many 1-year and 6-month. Approximately: several 1-year, two 6-month, so say 5-6 years, but calculate: at least 1 (Centre Head?) +1 (Toppr) +1 (Gyan360) +1 (Modern Chem) +1 (Pooja) +1 (Rukhma) +0.5 (Vidhyashree) +0.5 (Matushree) +1 (Physical Ed) – but some might overlap or not all specified. Text has multiple 1-year experiences, probably sequential. Total around 7-8 years, but not clear. Perhaps estimate from B.E in 2015, so 2015-2023, but MA running, so perhaps 8 years. Better to sum given durations: let's count: Centre Head no duration, but others: 1+1+1+1+1+0.5+0.5+1 = 7 years, plus Centre Head, say 1, total 8. But Centre Head duration not given. Perhaps leave as per calculation. I'll set to 7 or 8, but schema requires number, so I need to infer. From the list, minimum 6 entries of 1 year or more, but some might be part-time. I'll set to 7.0 based on sum.\n\nSkills: Not explicitly listed, so perhaps from context, like teaching chemistry, etc. But not a section, so maybe empty array.\n\nCertifications: CTET and MPTET are mentioned in academic records, but might fit here. Also, PGDCA, etc are degrees. Schema has Certifications separate, with name and issuing organization.\n\nFrom text, CTET qualified, MPTET qualified. Also, degrees might not be certifications.\n\nCTET is a certification/test. Similarly MPTET.\n\nIn Education, there is CTET in enum, but also Certifications section.\n\nPerhaps put test qualifications in Certifications.\n\nSchema Education has CTET in degree enum, so perhaps keep in Education.\n\nTo avoid duplication, since in text it's under Academic records, I'll keep in Education.\n\nAchievements: Listed on page 2, cricket selections, gold medal.\n\n- Selected for central zone cricket tournament\n\n- Selected for SGFI, KVS national cricket tournaments in various years, gold medal.\n\nSo, list as achievements with issuing organization, e.g., School Games Federation of India, KVS, etc.\n\nLanguages: Not mentioned, so empty array.\n\nProjects: Not mentioned, so empty.\n\nNow, for the table: It has Name, DOB, Father's name, Gender, Marital status, which I already used for PersonalInformation.\n\nNow, construct JSON.\n\nFirst, PersonalInformation:\n\n- FullName: \"Akash Gavlane\"\n\n- FullNameEmbedding: null\n\n- Gender: \"Male\" from table\n\n- BirthDate: \"1993-03-01\" (from \"01/03/1993\", assuming DD/MM/YYYY)\n\n- Email: \"<EMAIL>\"\n\n- Address: \"407 Navlakha, Indore 452001\"\n\n- City: \"Indore\"\n\n- State: \"Madhya Pradesh\" (inferred)\n\n- ContactNumber: \"8827781708\" (or both, but I'll put one)\n\n- LinkedInProfile: \"\"\n\nObjective: \"To Contribute best of my knowledge, skill and ability in the growth of the organization where I will be working and attaining the new heights in my field work.\"\n\nEducation: Array, ordered highest to lowest.\n\nDefine each:\n\n1. Degree: \"MA (Master of Arts)\", Specialization: \"English\", Institution: \"Rabindranath Tagore University\", GraduationYear: null, GPA: null, MaxGPA: null, Percentage: 80 (since mentioned)\n\n2. Degree: \"PGDCA (Post Graduate Diploma in Computer Applications)\", Specialization: \"\", Institution: \"Rabindranath Tagore University\", GraduationYear: null, GPA: null, MaxGPA: null, Percentage: 70\n\n3. Degree: \"BEd (Bachelor of Education)\", Specialization: \"\", Institution: \"Arihant College, Indore\", but affiliated to DAVV, so perhaps \"Arihant College\" or \"Devi Ahilya Vishwavidyalaya\". Text says \"B.ED From Arihant College 68% Indore DAVV.\", so Institution: \"Arihant College\", GraduationYear: null, Percentage: 68\n\n4. Degree: \"Post Graduation Diploma in Yogic therapy\", Specialization: \"Yogic therapy\", Institution: \"Devi Ahilya Vishwavidyalaya Indore\" (standardized), Percentage: 70, Year null\n\n5. Degree: \"BE (Bachelor of Engineering)\", Specialization: \"Mechanical Engineering\", Institution: \"Chameli Devi Group of Institutions\", GraduationYear: \"2015\", Percentage: 66\n\n6. Degree: \"12th Pass\", Specialization: \"\", Institution: \"Kendriya Vidyalaya Dewas\", GraduationYear: null, Percentage: 60\n\n7. Degree: \"10th Pass\", Specialization: \"\", Institution: \"Kendriya Vidyalaya Dewas\", GraduationYear: null, Percentage: 61\n\n8. Degree: \"CTET (Central Teacher Eligibility Test)\", Specialization: \"\", Institution: \"Central Board of Secondary Education\" (issuing body), but not specified, perhaps null or CBSE. Text says qualified, no institution. Schema has Institution for Education, so perhaps \"CBSE\" or leave as per knowledge. I'll put \"Central Board of Secondary Education\". Percentage: Paper I 75%, Paper II 65%, but it's one certification, perhaps average or not set. Schema has GPA/Percentage for Education, so perhaps set Percentage to null or average. Since it's a test, maybe not applicable. But required, so I'll set to null if not numerical.\n\nFor CTET, it's a qualification, not a degree with GPA. Similarly for MPTET.\n\nPerhaps for these, set Percentage if mentioned.\n\nFor CTET, perhaps \"75\" for Paper I, but it's combined. Text says \"CTET Qualified Paper I with 75% & Paper II with 65%\", so maybe two entries, but schema is per degree. I'll make one entry with Description or something, but schema doesn't have description for Education. Just set Specialization or something. Perhaps one entry with Degree \"CTET\", and Percentage not set.\n\nSchema required Percentage, so set to null.\n\nSimilarly for MPTET.\n\n9. Degree: \"MPTET (Madhya Pradesh Teacher Eligibility Test)\", Institution: \"\", Percentage: null, Year null\n\nOrder: MA > PGDCA > BEd > Diploma Yogic > BE > 12th > 10th > CTET > MPTET\n\nPGDCA and Diploma Yogic both post-grad diplomas, BEd is bachelor.\n\nStandardize InstitutionEmbedding null.\n\nWorkExperience: Array\n\nList:\n\n- Company: \"IIT-JEE|NEET Coaching Dewas\" or perhaps the coaching center name not specified, text says \"Centre Head of IIT-JEE|NEET Coaching Dewas\", so CompanyName: \"IIT-JEE NEET Coaching\" or \"Dewas Coaching Center\", but probably \"Unknown\" or use \"IIT-JEE NEET Coaching Institute Dewas\". Role: \"Centre Head\", StartYear null, EndYear null, Description: \"Head of coaching center for IIT-JEE and NEET\"\n\nDuration not given.\n\n- Company: \"Toppr app (Byjus)\", Role: \"Chemistry Teacher\", Duration: 1 year, so StartYear and EndYear null, Description: \"Taught Chemistry\"\n\nSimilarly for others.\n\nOrder latest first: assume Centre Head is latest, then others.\n\nTotal 7-8 experiences, sum durations.\n\nSet TotalWorkExperienceInYears to 7 (approximate)\n\nSkills: not listed, so []\n\nCertifications: perhaps CTET and MPTET can be here, but already in Education. To avoid duplication, don't repeat.\n\nSchema has separate Certifications, so if not in Education, put here. But CTET is in Education enum, so probably keep there.\n\nFrom text, no other certifications mentioned explicitly.\n\nAchievements: \n\n- AchievementName: \"Selected for central zone cricket tournament 2014\", IssuingOrganization: \"Cricket Association or School\", not specified. From text, \"Selected for central zone cricket tournament which was held in 2014 at Warangal\", so perhaps \"Central Zone Cricket Tournament\"\n\nBetter to extract as is.\n\nList several cricket selections.\n\nLanguages: not mentioned, []\n\nProjects: not mentioned, []\n\nNow, for embeddings, all null.\n\nOutput must be JSON string.\n\nEnsure no duplication.\n\nFor WorkExperience, Role must include expertise, e.g., \"Chemistry Teacher\" already has.\n\nNow, write the JSON."}}], "created": 1750944043, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 2008, "prompt_tokens": 5703, "total_tokens": 12805, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 5094, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4226, "text_tokens": 5703, "image_tokens": 0}, "num_sources_used": 0}}