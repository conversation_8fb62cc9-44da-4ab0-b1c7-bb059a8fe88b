{"id": "007db842-a841-4e70-845f-d4b3ceff05db", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"Pooja Pandey\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Female\",\n      \"BirthDate\": \"1985-06-21\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"507-Satelite Township, Mundi Road Bijalpur, Indore-452012 (Madhya Pradesh)\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"9301077733\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"Total 10 years of Teaching experience of Class VI to X (High School) Hindi & English.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MA (Master of Arts)\",\n        \"Specialization\": \"Hindi\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2016-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 70.0\n      },\n      {\n        \"Degree\": \"MA (Master of Arts)\",\n        \"Specialization\": \"English\",\n        \"Institution\": \"<PERSON>vidyal<PERSON>, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2014-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 60.5\n      },\n      {\n        \"Degree\": \"BEd (Bachelor of Education)\",\n        \"Specialization\": \"Education\",\n        \"Institution\": \"Satya Sai Women College, Bhopal\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2011-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 72.5\n      },\n      {\n        \"Degree\": \"PGDCA\",\n        \"Specialization\": \"Computer\",\n        \"Institution\": \"Makhanlal Chaturvedi National University of Journalism and Communication, Bhopal\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2009-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 61.0\n      },\n      {\n        \"Degree\": \"BA (Bachelor of Arts)\",\n        \"Specialization\": \"English, Economics, Sociology\",\n        \"Institution\": \"Bundelkhand University, Jhansi\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2004-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 55.8\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"English, Hindi, Psychology, Economics, Sociology\",\n        \"Institution\": \"U.P. Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2001-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 57.2\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"All Subjects\",\n        \"Institution\": \"U.P. Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"1999-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 65.2\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Chanderbala Modi Academy Ankleshwar\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"High School Hindi Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2022-06-01\",\n        \"EndYear\": \"2023-03-31\",\n        \"Description/Responsibility\": \"Teaching of Hindi Lesson class VI to X, Counseling of teen ages students as well as their parents, Conducting Parents Teacher meet (PTM). In charge of Morning /Dispersal assembly, Discipline, Language club, Fitness club (Yoga & Zumba), innovative practice of learning, Cultural programs (Annual function birthday celebrations, Investiture ceremony, Graduation ceremony, National festival on 15th Aug & 26th Jan), In-house competition (Elocution, debate, hand writing). ASL: Assessment of speaking and listening as per CBSE guidelines using audio visual method. Oral exams/Remedial classes conduction for specific students. Preparation of Book list for VI to X, Notice writing (drafting). Art and Craft work (soft board decorations and ART exhibition). Monitoring & Reporting of Health & Hygiene of students and classes. Attended CBSE workshops & teachers training course like: Bloom's Taxonomy, Classroom management, Pariksha Pe Charcha, DIKSHA Competency Based Education Module. Preparation of Question bank & worksheets, Pre-Board exams question paper.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Suryadatta National School Pune\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"High School Hindi Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2017-04-01\",\n        \"EndYear\": \"2022-04-30\",\n        \"Description/Responsibility\": \"Teaching of Hindi Lesson class VI to X, Counseling of teen ages students as well as their parents, Conducting Parents Teacher meet (PTM). Registration for Std 9 & 10 students, Internal marks upload on CBSE website & CBSE 10th board result analysis. In charge of Morning /Dispersal assembly, Discipline, Language club, Fitness club (Yoga & Zumba), innovative practice of learning, Cultural programs (Annual function birthday celebrations, Investiture ceremony, Graduation ceremony, National festival on 15th Aug & 26th Jan), In-house competition (Elocution, debate, hand writing). ASL: Assessment of speaking and listening as per CBSE guidelines using audio visual method. Oral exams/Remedial classes conduction for specific students. Preparation of Time Table & Annual Planner/Calendar, Lesson plans, Activity book, question paper, Answer Keys, CCE File, Mark lists (Excel) Attendance record book, anecdote, Report card. Cover Duty (Substitution & Invigilation duties), Handling exam team for question papers and answer keys collection & evaluation of answer sheets. Preparation of Book list for VI to X, Notice writing (drafting). Art and Craft work (soft board decorations and ART exhibition). Monitoring & Reporting of Health & Hygiene of students and classes. Attended CBSE workshops & teachers training course like: Bloom's Taxonomy, Classroom management, Pariksha Pe Charcha, DIKSHA Competency Based Education Module. Preparation of Question bank & worksheets, Pre-Board exams question paper.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"KPS Inter College Banda\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"High School English Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2004-07-01\",\n        \"EndYear\": \"2008-06-30\",\n        \"Description/Responsibility\": \"Teaching of English Lesson class VI to X, Counseling of teen ages students as well as their parents, Conducting Parents Teacher meet (PTM). ASL: Assessment of speaking and listening using audio visual method.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 10,\n    \"Skills\": [\n      \"Hindi Language Teaching\",\n      \"English Language Teaching\",\n      \"Student Counseling\",\n      \"Computer Typing\",\n      \"Online Teaching\",\n      \"Event Management\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [],\n    \"Achievements\": [],\n    \"Languages\": [\n      \"English\",\n      \"Hindi\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent, which is a PDF text extraction in a CSV-like structure with page numbers, text, and bounding box coordinates. There are also tables provided separately. My task is to extract structured data from this and output it in a specific JSON format based on the <user_json_schema>.\n\nStep 1: Analyze the UserContent. It's unstructured text with coordinates, so I need to reconstruct the layout using the bounding box coordinates (X1, Y1, X2, Y2) to understand the relative positions and group related text.\n\nStep 2: Reconstruct the layout. The coordinates help determine the order and grouping of text. For example, text with similar Y-coordinates might be on the same line or in the same section. I need to parse this to identify sections like personal info, work experience, education, etc.\n\nFrom the text, I see:\n\n- Page 1 has sections like RESUME, full name, address, mobile, email, synopsis, highlights, work experience.\n\n- Page 2 continues work experience and has more details.\n\n- Page 3 has \"Education:\" and \"Personal Details :\".\n\n- Page 4 has references and declaration.\n\nThere are two tables: Table-1 for education and Table-2 for personal details.\n\nSince tables are provided separately, I can use them directly for structured data extraction.\n\nStep 3: Extract relevant information and fill the output JSON. If something isn't found, leave it as null or empty string as specified.\n\nStep 4: Ensure no duplication, handle typos, and assign to appropriate categories.\n\nStep 5: If not specified, set to null.\n\nNow, let's map the data to the JSON schema.\n\nThe JSON has a \"Resume\" object with several properties.\n\nStart with \"PersonalInformation\":\n\n- FullName: From page 1, \"POOJA PANDEY\" is clearly the name.\n\n- FullNameEmbedding: Leave as null, as it's server-generated.\n\n- Gender: Not explicitly stated, but inferred from name. \"Pooja\" is a common female name in India, so infer \"Female\".\n\n- BirthDate: From Table-2, \"D.O.B / Sex : 21th June, 1985/Female\". So, \"1985-06-21\" (need to format as date).\n\n- Email: From page 1, \"Mobile No. 9301077733 Email: <EMAIL>, <EMAIL>\". So, probably \"<EMAIL>\" or both? The schema expects a single string, so I might need to choose one or concatenate. But it's listed as one field, so perhaps take the primary one. I'll use \"<EMAIL>\" as it's first.\n\n- Address: From page 1, \"507-Satelite Township, Mundi Road Bijalpur, Indore-452012 (Madhya Pradesh)\". Also in Table-2.\n\n- City: From address, \"Indore\".\n\n- State: \"Madhya Pradesh\".\n\n- ContactNumber: \"9301077733\".\n\n- LinkedInProfile: Not mentioned anywhere, so null or empty string. Schema says string, but if not found, should be '' or null. From step 3, if not found, keep as ''. But in schema, it's type string, so probably an empty string.\n\nIn the schema, all fields in PersonalInformation are required, but the description says if not found, keep as ''. So I'll set missing fields to empty strings or null where applicable.\n\nBirthDate can be null, others are strings.\n\nLinkedInProfile not found, so ''.\n\nNow, Gender: Inferred as Female.\n\nBirthDate: \"21th June, 1985\" – typo, should be \"21st June, 1985\". Format as \"1985-06-21\".\n\nEmail: I'll use \"<EMAIL>\" as primary, but there's also \"<EMAIL>\". Perhaps list both, but schema is a single string. I'll put them together: \"<EMAIL>, <EMAIL>\".\n\nAddress: \"507-Satelite Township, Mundi Road Bijalpur, Indore-452012 (Madhya Pradesh)\"\n\nCity: \"Indore\"\n\nState: \"Madhya Pradesh\". It's in the enum list as \"Madhya Pradesh\".\n\nContactNumber: \"9301077733\"\n\nLinkedInProfile: Not found, so \"\"\n\nNext, Objective: In the schema, it's a string for career objective. From page 1, \"Synopsis: Total 10 years of Teaching experience of Class VI to X (High School) Hindi & English.\" This seems like a summary, so I can use this as the objective.\n\nEducation: There's a Table-1 provided with degrees, years, etc. I need to extract in order from highest to lowest: PhD > Masters > Bachelors > etc.\n\nTable-1:\n\n- M.A. (Hindi), 2016, Old GDC DAVV Indore, Hindi, 70.0%\n\n- M.A. (English), 2014, Old GDC DAVV Indore, English, 60.5%\n\n- B.Ed., 2011, Satya Sai Women College Barkatullah University Bhopal, Education, 72.5%\n\n- PGDCA, 2009, Makhanlal Chaturvedi National University of Journalism and Communication Bhopal, Computer, 61.0%\n\n- B.A., 2004, Bundelkhand University, Jhansi, English, Economics, Sociology, 55.8%\n\n- Intermediate, 2001, U.P. Board, English, Hindi, Psychology, Economics, Sociology, 57.2%\n\n- High School, 1999, U.P. Board, All Subjects, 65.2%\n\nNeed to order from highest to lowest. M.A. is Masters, B.Ed. might be considered, PGDCA is diploma, B.A. is bachelors, Intermediate is 12th, High School is 10th.\n\nStandard order: PhD > Masters (M.A.) > Bachelors (B.A.) > Diploma (PGDCA, B.Ed.?) B.Ed. is a bachelor's level in some contexts, but in the enum, it's listed separately. The note says \"PhD > Masters > Bachelors > Diploma > 12th Pass > 10th Pass\".\n\nIn the enum, B.Ed. is under bachelors, but it's a postgraduate diploma in some systems. However, in the list, it's specified, so I'll follow the degrees.\n\nList:\n\n- M.A. (Hindi) 2016\n\n- M.A. (English) 2014\n\n- B.Ed. 2011 – this is Bachelor of Education, so bachelors level?\n\nIn the enum, B.Ed. is listed, and in the order, it should be after masters if M.A. is masters.\n\nTypically, M.A. is masters, B.Ed. can be done after bachelors, so order should be M.A. > B.Ed. > B.A. > PGDCA (diploma) > Intermediate (12th) > High School (10th).\n\nPGDCA is Post Graduate Diploma, so it might be after masters or with diplomas.\n\nBut the instruction says \"PhD > Masters > Bachelors > Diploma > 12th Pass > 10th Pass\".\n\nSo, Masters: M.A. Hindi and M.A. English\n\nBachelors: B.Ed. and B.A. – B.Ed. is listed as B.Ed in enum, which is under bachelors in the anyOf.\n\nIn the schema, Degree has enum including \"MA (Master of Arts)\", \"BEd (Bachelor of Education)\", etc.\n\nSo, for each entry:\n\n- Degree: Match to enum if possible. M.A. should be \"MA (Master of Arts)\", but it's abbreviated. Use the enum values.\n\nSchema says \"Try to match the list, else allow fallback.\" So for M.A., use \"MA (Master of Arts)\", similarly for others.\n\n- Specialization: From the subject column.\n\n- Institution: Need standardized name. For example, \"Old GDC DAVV Indore\" – probably \"Devi Ahilya Vishwavidyalaya Indore\" or something. But it says \"Old GDC DAVV Indore\", and in schema, \"Avoid abbreviations, return full standardized name.\" I need to infer. GDC might be Government Degree College, DAVV is Devi Ahilya Vishwavidyalaya. Perhaps \"Devi Ahilya Vishwavidyalaya, Indore\". Similarly for others.\n\nFrom the text, it's \"Old GDC DAVV Indore\", but in Table-1, it's \"Old GDC DAVV Indore\". I should use \"Devi Ahilya Vishwavidyalaya, Indore\" as standardized.\n\nSimilarly, \"Satya Sai Women College Barkatullah University Bhopal\" – institution is \"Satya Sai Women College\", university is Barkatullah University.\n\nSchema has \"Institution\" as the educational institution, so probably \"Satya Sai Women College, Bhopal\" affiliated to Barkatullah University.\n\nBut schema says \"full, standardized name\", so I'll try to clean it up.\n\n- GraduationYear: From Year column, but it's year, not full date. Schema format is date, but it's just year, so perhaps \"2016-01-01\" or just the year string? Schema says format \"date\", but if only year is given, I can use \"YYYY-01-01\" or something, but perhaps keep as string with year.\n\nSchema says \"format\":\"date\", so I need to provide in ISO format. If only year, perhaps \"2016\" as string, but format date might expect full date. In JSON schema, it's string with format date, but I can put \"2016\" and it might be parsed.\n\nTo be safe, I'll put the year as \"YYYY\" since exact date isn't given.\n\nLooking at schema, GraduationYear is \"type\":[\"string\",\"null\"],\"format\":\"date\", so I'll use \"2016-01-01\" assuming January 1st, or just \"2016\". I'll use full date for consistency, but since only year, perhaps \"2016\".\n\nIn the table, it's just year, so I'll use \"2016\" as string.\n\n- GPA: Not mentioned, but there's Percentage. Schema has GPA and Percentage. It says \"If mentioned, extract GPA value\", else perhaps null. Here, percentage is given.\n\n- MaxGPA: Need to infer scale. For percentage system, max GPA might not apply, but schema has it. It says \"infer using knowledge\". In Indian system, percentage is out of 100, so MaxGPA could be 100, but GPA is usually on 4.0 or 10.0 scale. Perhaps for percentage-based, set GPA to null and use Percentage.\n\nSchema says: \"Percentage\":{\"type\":\"number\",\"description\":\"Convert GPA to percentage using: (GPA / MaxGPA) * 100. If percentage is explicitly mentioned, extract it.\"}\n\nHere, percentage is mentioned, so extract percentage directly.\n\nGPA and MaxGPA can be null if not mentioned.\n\nIn this case, no GPA mentioned, percentage is given, so set Percentage to the value, GPA and MaxGPA to null.\n\nSchema requires them: \"required\":[\"Degree\",\"Specialization\",\"Institution\",\"InstitutionEmbedding\",\"GraduationYear\",\"GPA\",\"MaxGPA\",\"Percentage\"]\n\nGPA and MaxGPA are required, but if not found, I need to set to null, I think, even though required in schema, but instruction says if not found, keep as '' or null.\n\nIn JSON schema, required means they must be present, but in value, they can be null if type allows. GPA is \"type\":\"number\", not nullable, but in description, it might be inferred.\n\nGPA type is \"number\", not array or null, so perhaps I need to provide a number or omit, but schema says required, so I should set to null if not applicable, but JSON doesn't allow null for number type strictly, but in practice, I can use null.\n\nIn the properties, GPA is \"type\":\"number\", but in the array items, it's part of the object.\n\nI'll set GPA and MaxGPA to null if not mentioned.\n\nSimilarly for others.\n\nFirst, order the education: Highest to lowest.\n\nM.A. (Hindi) 2016, M.A. (English) 2014 – both masters, but chronologically, 2016 is later, but order by level, not year. Schema says \"Highest to Lowest order, like PhD, MBA, MSc, BSc, 10th Pass, 12th Pass.\" So by level, not by time.\n\nSo Masters first: M.A. Hindi and M.A. English. I can list M.A. Hindi first if it's later, but typically by level and then by date descending or ascending. Schema doesn't specify, but probably by level.\n\nI'll list in the order of degree level: Masters, then Bachelors, etc.\n\nM.A. are both masters, so perhaps alphabetically or by year. I'll list as per table or chronologically reverse.\n\nTable is not ordered, but I'll sort by level.\n\nDefine levels:\n\n- M.A.: Master of Arts\n\n- B.Ed.: Bachelor of Education\n\n- PGDCA: Diploma (Post Graduate Diploma)\n\n- B.A.: Bachelor of Arts\n\n- Intermediate: 12th Pass\n\n- High School: 10th Pass\n\nOrder: M.A. (both), then B.Ed., then PGDCA (diploma), then B.A. (bachelors), then Intermediate, then High School. B.A. is bachelors, B.Ed. might be considered bachelors or diploma.\n\nIn schema enum, B.Ed is under bachelors, PGDCA isn't listed, but fallback.\n\nStandard order: Masters > Bachelors > Diploma > 12th > 10th.\n\nSo M.A. > B.Ed. > PGDCA > B.A. > Intermediate > High School? B.A. is before PGDCA? PGDCA is post-graduate, so after bachelors.\n\nPGDCA is Post Graduate Diploma, so it should be after masters or with diplomas.\n\nFrom the years: M.A. 2016 and 2014, B.Ed. 2011, PGDCA 2009, B.A. 2004, etc. So chronologically, education is in reverse, but I need highest level first.\n\nLevel: M.A. is highest, then B.Ed. (bachelors level), PGDCA (di Diploma), B.A. (bachelors), but B.Ed. and B.A. both bachelors.\n\nPerhaps group by degree type.\n\nI'll list:\n\n1. M.A. (Hindi) 2016\n\n2. M.A. (English) 2014\n\n3. B.Ed. 2011\n\n4. PGDCA 2009\n\n5. B.A. 2004\n\n6. Intermediate 2001\n\n7. High School 1999\n\nFor each:\n\n- Degree: Match to enum. M.A. (Hindi) -> \"MA (Master of Arts)\", Specialization \"Hindi\"\n\n- Institution: Standardize. \"Old GDC DAVV Indore\" -> probably \"Devi Ahilya Vishwavidyalaya, Indore\". Similarly, \"Satya Sai Women College Barkatullah University Bhopal\" -> \"Satya Sai Women College, Bhopal\" (affiliated to Barkatullah University, but institution is the college).\n\nSchema says \"educational institution\", so \"Government Degree College, Indore\" or something, but it's \"Old GDC DAVV Indore\", I'll use \"Devi Ahilya Vishwavidyalaya, Indore\" as it's the university.\n\nIn Table-1, it's \"Board/University\", but for institution, it's part of it.\n\nFor \"Old GDC DAVV Indore\", GDC might be Government Degree College, DAVV is the university. But schema wants the institution name. Perhaps \"Government Degree College, Indore\" under Devi Ahilya Vishwavidyalaya.\n\nTo simplify, I'll use the name as is or standardize.\n\nFor consistency, use \"Devi Ahilya Vishwavidyalaya, Indore\" for M.A. entries.\n\nFor B.Ed., \"Satya Sai Women College, Bhopal\"\n\nFor PGDCA, \"Makhanlal Chaturvedi National University of Journalism and Communication, Bhopal\"\n\nFor B.A., \"Bundelkhand University, Jhansi\" – but it's the university, institution might be the college, but not specified, so use \"Bundelkhand University, Jhansi\"\n\nFor Intermediate and High School, board is U.P. Board, so institution might be the school, not specified.\n\nIn schema, for 12th and 10th, institution could be the board or school, but not given, so perhaps use the board name.\n\nIn Table-1, for Intermediate and High School, no specific institution, only Board/University as \"U.P. Board\".\n\nSo for Institution, set to \"U.P. Board\" or the school if known, but not known, so \"Unknown\" or use board.\n\nSchema doesn't specify, so I'll use the board for school levels.\n\n- GraduationYear: \"2016\", etc.\n\n- GPA: null, since not mentioned.\n\n- MaxGPA: null\n\n- Percentage: given, e.g., 70.0 for M.A. Hindi\n\nSpecialization: for M.A. Hindi, \"Hindi\", for B.A., \"English, Economics, Sociology\", but schema is string, so keep as is.\n\nInstitutionEmbedding: null\n\nNow, WorkExperience: From the text, there are three organizations.\n\nOrder: Highest to lowest role and latest first. Roles are all \"High School Hindi Teacher\" or \"High School English Teacher\", with grades TGT or PGT.\n\nFrom Synopsis, total 10 years, and experiences:\n\n- Organization-03: Chanderbala Modi Academy, June 2022 to March 2023, Role: High School Hindi Teacher\n\n- Organization-02: Suryadatta National School Pune, April 2017 to April 2022 (5 years), Role: High School Hindi Teacher, Grade PGT\n\n- Organization-01: K PS Inter College Banda, July 2004 to June 2008 (4 years), Role: High School English Teacher\n\nRoles: All teacher roles, but perhaps order by latest first.\n\nSchema says \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern as well as with latest work experience first\"\n\nSo probably latest first, and within that, by seniority if applicable. Here, all similar roles, so latest first.\n\nSo order:\n\n1. Chanderbala Modi Academy, 2022-2023, High School Hindi Teacher\n\n2. Suryadatta National School, 2017-2022, High School Hindi Teacher\n\n3. K PS Inter College, 2004-2008, High School English Teacher\n\nFor each:\n\n- CompanyName: e.g., \"Chanderbala Modi Academy Ankleshwar\"\n\n- Role: \"High School Hindi Teacher\" – must include expertise, which is Hindi or English.\n\n- StartYear: \"2022-06-01\" or just \"2022\"? Use date format. From \"June 2022\", so \"2022-06-01\"\n\n- EndYear: \"March 2023\" -> \"2023-03-31\"\n\n- Description: From the job profile sections.\n\nFor Organization-03, there's a detailed job profile.\n\nSimilarly for others.\n\n- DescriptionEmbedding: null\n\nTotalWorkExperienceInYears: Synopsis says 10 years, calculate from experiences: 2023-2022=1 year (approx), 2022-2017=5 years, 2008-2004=4 years, total 10 years, yes.\n\nSkills: Not explicitly listed, but from highlights and job profiles, e.g., \"Good Computer Knowledge of Hindi and English Typing, Online teaching...\", \"Teaching\", etc. Also from Table-2, \"Languages Known : English & Hindi\"\n\nSkills might include teaching skills, computer skills, etc. I need to extract a list.\n\nFrom text: \"Excellent ability to engage students...\", \"Good Computer Knowledge of Hindi and English Typing, Online teaching via Zoom, Google Classroom, Teams & Webex\", etc.\n\nSo possible skills: Teaching, Student Counseling, Computer Skills, Online Teaching, Hindi Typing, English Typing, etc.\n\nBut need to list as array of strings. Perhaps infer from context.\n\nAlso, in highlights and job profiles.\n\nCertifications: Not explicitly mentioned, except perhaps B.Ed. is a certification, but it's under education. In the education table, B.Ed. is listed, so probably not separate certifications.\n\nIn job profiles, attended workshops, but not formal certifications.\n\nFrom text, \"Attended CBSE workshops & teachers training course\", but not specified as certifications.\n\nSo perhaps no certifications, or infer B.Ed. as certification, but it's in education.\n\nSchema has Certifications as separate, with name, issuing organization, etc.\n\nNot mentioned, so empty array or null, but schema requires array, so empty array.\n\nAchievements: Not mentioned explicitly. In job profiles, there might be implied, but nothing specific. So empty array.\n\nLanguages: From Table-2, \"Languages Known : English & Hindi\". Also in synopsis, teaching Hindi and English.\n\nSo [\"English\", \"Hindi\"]\n\nProjects: Not mentioned in the resume. So empty array.\n\nObjective: Use the synopsis as objective.\n\nNow, for embeddings, all should be null as per instruction.\n\nOutput must be a single JSON string in the specified format.\n\nNow, handle the reconstruction using coordinates if needed, but since tables are provided, and main text is sequential, I think I have most data.\n\nFor PersonalInformation, from Table-2 and page 1.\n\nTable-2 has more details: Husband's name, etc., but schema only asks for specific fields, so don't add extra.\n\nNow, compile the JSON.\n\nFirst, PersonalInformation:\n\n- FullName: \"Pooja Pandey\"\n\n- FullNameEmbedding: null\n\n- Gender: \"Female\" (inferred)\n\n- BirthDate: \"1985-06-21\" (from \"21th June, 1985\", correct typo)\n\n- Email: \"<EMAIL>, <EMAIL>\" or just one? I'll put both.\n\n- Address: \"507-Satelite Township, Mundi Road Bijalpur, Indore-452012 (Madhya Pradesh)\"\n\n- City: \"Indore\"\n\n- State: \"Madhya Pradesh\"\n\n- ContactNumber: \"9301077733\"\n\n- LinkedInProfile: \"\"\n\nObjective: \"Total 10 years of Teaching experience of Class VI to X (High School) Hindi & English.\"\n\nEducation: Array of objects, ordered highest to lowest.\n\nDefine the list:\n\n1. Degree: \"MA (Master of Arts)\", Specialization: \"Hindi\", Institution: \"Devi Ahilya Vishwavidyalaya, Indore\", GraduationYear: \"2016\", GPA: null, MaxGPA: null, Percentage: 70.0\n\nInstitution: from \"Old GDC DAVV Indore\", standardize to \"Devi Ahilya Vishwavidyalaya, Indore\"\n\nSimilarly,\n\n2. \"MA (Master of Arts)\", \"English\", \"Devi Ahilya Vishwavidyalaya, Indore\", \"2014\", null, null, 60.5\n\n3. \"BEd (Bachelor of Education)\", \"Education\", \"Satya Sai Women College, Bhopal\", \"2011\", null, null, 72.5\n\n4. For PGDCA, not in enum, so use fallback \"PGDCA\", Specialization: \"Computer\", Institution: \"Makhanlal Chaturvedi National University of Journalism and Communication, Bhopal\", \"2009\", null, null, 61.0\n\n5. \"BA (Bachelor of Arts)\", \"English, Economics, Sociology\", \"Bundelkhand University, Jhansi\", \"2004\", null, null, 55.8\n\n6. \"12th Pass\", Specialization: \"English, Hindi, Psychology, Economics, Sociology\", Institution: \"U.P. Board\", \"2001\", null, null, 57.2\n\n7. \"10th Pass\", Specialization: \"All Subjects\", Institution: \"U.P. Board\", \"1999\", null, null, 65.2\n\nFor 12th and 10th, degree should be \"12th Pass\" and \"10th Pass\" as per enum.\n\nSpecialization can be the subjects.\n\nInstitution is the board, since school not specified.\n\nWorkExperience:\n\nArray, latest first.\n\n1. CompanyName: \"Chanderbala Modi Academy Ankleshwar\", Role: \"High School Hindi Teacher\", StartYear: \"2022-06-01\", EndYear: \"2023-03-31\", Description: from the job profile text.\n\nDescription for first one: \"Teaching of Hindi Lesson class VI to X, Counseling of teen ages students as well as their parents, Conducting Parents Teacher meet (PTM). In charge of Morning /Dispersal assembly, Discipline, Language club, Fitness club (Yoga & Zumba), innovative practice of learning, Cultural programs... etc.\" I need to concatenate the description from the text.\n\nSimilarly for others.\n\nTotalWorkExperienceInYears: 10\n\nSkills: Infer from text. Possible: [\"Teaching\", \"Student Counseling\", \"Hindi Typing\", \"English Typing\", \"Online Teaching\", \"CBSE Workshop Attendance\"] but need to be precise. From highlights: \"Excellent ability to engage students\", \"Good Computer Knowledge\", etc. Perhaps list: [\"Hindi Teaching\", \"English Teaching\", \"Computer Skills\", \"Online Teaching Tools\"]\n\nBut better to extract mentioned skills. From synopsis and highlights: Teaching experience, computer knowledge, typing, online tools.\n\nAlso from Table-2, languages, but that's separate.\n\nI'll put [\"Teaching\", \"Counseling\", \"Computer Typing\", \"Online Teaching\", \"Event Management\"] or something. Need to be accurate.\n\nPerhaps: [\"Hindi Language Teaching\", \"English Language Teaching\", \"Computer Applications\", \"Online Education Platforms\"]\n\nCertifications: Perhaps B.Ed. is a certification, but it's in education. No other mentioned, so empty array.\n\nAchievements: None mentioned, empty array.\n\nLanguages: [\"English\", \"Hindi\"]\n\nProjects: Empty array.\n\nNow, for descriptions in WorkExperience, I need to copy the relevant text.\n\nFor Organization-03: The job profile from page 1.\n\nSimilarly for others.\n\nAlso, SkillsEmbedding, etc., null.\n\nFinally, output as JSON string."}}], "created": 1750943874, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 1687, "prompt_tokens": 7618, "total_tokens": 14858, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 5553, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4232, "text_tokens": 7618, "image_tokens": 0}, "num_sources_used": 0}}