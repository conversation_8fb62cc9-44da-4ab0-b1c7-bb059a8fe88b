{"id": "755d1409-c41f-42df-b7b5-7b0b87e3cee1", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"Anki<PERSON> Vyas\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Male\",\n      \"BirthDate\": \"1995-06-10\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"113, New Durga Colony Marimata Square, Indore\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"9826765004\",\n      \"LinkedInProfile\": \"<EMAIL>\"\n    },\n    \"Objective\": \"\",\n    \"Education\": [\n      {\n        \"Degree\": \"PhD\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Mandsaur University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      },\n      {\n        \"Degree\": \"MPEd (Master of Physical Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"School of Physical Education, Devi Ahilya Vishwavidyalaya, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2018-01-01\",\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      },\n      {\n        \"Degree\": \"BPEd (Bachelor of Physical Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"School of Physical Education, Devi Ahilya Vishwavidyalaya, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2015-01-01\",\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      },\n      {\n        \"Degree\": \"Diploma in Sports Coaching\",\n        \"Specialization\": \"Gymnastics\",\n        \"Institution\": \"Netaji Subhas National Institute of Sports, Patiala\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2019-01-01\",\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Devi Ahilya Shishu Vihar High Secondary School, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Bal Niketan Sangh Pagnispaga High Secondary School\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0.0,\n        \"MaxGPA\": 0.0,\n        \"Percentage\": 0.0\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Modern International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physical Education Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Working as Physical Education Teacher with 3 years of experience in Indore, Madhya Pradesh.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Indore Public School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physical Education Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Working as Physical Education Teacher with 2 years of experience in Jhabua.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"South Valley International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physical Education Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Working as Physical Education Teacher with 2 years of experience in Betma.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Indira Gandhi Stadium\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Gymnastics Coach Intern\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2019-01-01\",\n        \"EndYear\": \"2019-02-28\",\n        \"Description/Responsibility\": \"Worked as Gymnastics Coach during internship for 2 months.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Sanmmati High Secondary School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Physical Education Teacher Intern\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2017-01-01\",\n        \"EndYear\": \"2017-02-14\",\n        \"Description/Responsibility\": \"Worked as P.E.T during internship for 45 days in Indore, Madhya Pradesh.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 7.0,\n    \"Skills\": [\n      \"Research in Physical Education and Sports\",\n      \"Teaching Physical Education and Sports\",\n      \"Coaching in Artistic Gymnastics\",\n      \"Sports Biomechanics\",\n      \"Sports Training\",\n      \"Gymnastics in Physical Education\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"Silver Medal on High Bar\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh Sub Junior and Junior Boys/Girls Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2007-09-20\"\n      },\n      {\n        \"AchievementName\": \"Bronze Medal on Floor Exercise\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-10-15\"\n      },\n      {\n        \"AchievementName\": \"Silver Medal on Floor Exercise and Bronze Medal on Roman Rings\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2009-09-18\"\n      },\n      {\n        \"AchievementName\": \"Gold Medal on Parallel Bars and Silver Medal on Horizontal Bar and Vaulting Table\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2010-11-12\"\n      },\n      {\n        \"AchievementName\": \"Silver Medal on Parallel Bars\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2010-11-24\"\n      },\n      {\n        \"AchievementName\": \"Gold Medal on Parallel Bars, Vaulting Table, High Bar and Silver Medal on Roman Rings and Floor Exercise, All-Round Champion\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Artistic Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-29\"\n      },\n      {\n        \"AchievementName\": \"Silver Medal on Parallel Bars and Vaulting Table\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2012-12-05\"\n      },\n      {\n        \"AchievementName\": \"Bronze Medal on Parallel Bars, Vaulting Table, Horizontal Bar, and Roman Rings\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhya Pradesh State Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2012-12-05\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"21st Sub Junior National Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2007-12-18\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Sports Authority of India All India Inter Regional Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2007-10-05\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"54th School National Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-12-30\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"24th Sub Junior National Artistic Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-12-30\"\n      },\n      {\n        \"AchievementName\": \"Participation in Team Championship\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"55th School National Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2009-10-27\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"56th School National Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-01-19\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"57th School National Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2012-05-05\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"All India Inter University Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2015-12-19\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"All India Inter University Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2016-01-11\"\n      },\n      {\n        \"AchievementName\": \"Participation\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"All India Inter University Gymnastics Championship\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2018-06-19\"\n      }\n    ],\n    \"Languages\": [],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is in a CSV-like structure with Page No., Text, X1, Y1, X2, Y2 coordinates. There's also a Table-1 with specific key-value pairs. I need to reconstruct the layout using the bounding box coordinates to ensure I extract information accurately.\n\nStep 1: Analyze the UserContent. It's unstructured text from a PDF, with coordinates indicating positions. I must consider the relative locations to group related information.\n\nStep 2: Reconstruct the layout. The coordinates (X1, Y1, X2, Y2) represent bounding boxes. Typically, in PDFs, Y increases downwards, so higher Y values are lower on the page. I need to group texts that are close in position to form logical sections like headers, education, experience, etc.\n\nLooking at the text:\n\n- Page 1 starts with \"CURRICULUM VITAE\" at the top.\n\n- Then \"UNDERGRADUATE AND POST GRADUATE COLLAGE\" – probably a typo for \"COLLEGE\".\n\n- \"ATTENDED\" followed by high school and intermediate details.\n\n- Then degrees like Bachelor and Master in Physical Education.\n\n- \"DEGREE AWARDED\" repeats some education info.\n\n- \"Diploma in sports coaching\", \"Pursuing PhD\".\n\n- \"AREA OF SPECIAL INTEREST\" and \"AREA OF SPECIALIZATION\".\n\n- Page 2 continues with more areas, \"PROFESSIONAL EXPERIENCE\", awards, etc.\n\n- Pages 3 and 4 have more participation and awards.\n\n- There's a Table-1 with personal info: Name, Place of Birth, Date of Birth, Address, Contact No.\n\nI need to extract all relevant info and fit it into the specified JSON schema.\n\nStep 3: Fill out the required output based on the schema. If not found, leave as null or empty string.\n\nKey sections from schema:\n\n- PersonalInformation: FullName, Gender (infer from name), BirthDate, Email (not mentioned, so null), Address, City, State, ContactNumber, LinkedInProfile (not mentioned, so null).\n\nFrom Table-1:\n\n- FullName: Ankit Vyas\n\n- BirthDate: 10.06.1995 – should be in date format, probably \"1995-06-10\"\n\n- Address: \"113, New Durga Colony Marimata Square, Indore\"\n\n- City: Indore (from address and other mentions)\n\n- State: Madhya Pradesh (mentioned multiple times)\n\n- ContactNumber: 9826765004\n\n- Gender: Infer from name \"Ankit Vyas\". Ankit is a common male name in India, so infer \"Male\".\n\n- Email: Not mentioned, so null or empty.\n\n- LinkedInProfile: Not mentioned, so null.\n\nFullNameEmbedding, etc., are to be left null as per instructions.\n\n- Objective: Not explicitly stated in the resume. Looking through the text, there's no career objective or summary. So, this should be an empty string.\n\n- Education: Need to list in highest to lowest order: PhD, Masters, Bachelors, etc.\n\nFrom the text:\n\n- Pursuing PhD from Mandsaur University, Mandsaur (M.P.) – not completed, so perhaps not include or handle as current.\n\nThe schema requires \"GraduationYear\", which might be null for ongoing. But it's listed as required, so I need to include it. For pursuing, GraduationYear could be null.\n\nEducation entries:\n\n- Bachelor of Physical Education, 2015 from School of Physical Education, DAVV, Indore.\n\n- Master of Physical Education, 2018 from School of Physical Education, DAVV, Indore.\n\n- Diploma in sports coaching, Gymnastics from NSNIS, Patiala, 2018-19. – This might be a diploma, so order: Masters > Bachelors > Diploma.\n\n- Also, high school and intermediate mentioned under \"ATTENDED\".\n\nFrom page 1:\n\n- High School from Bal Niketan Sangh Pagnispaga High Secondary School\n\n- Intermediate from Devi Ahilya Shishu Vihar High Secondary School, Indore (Madhya Pradesh)\n\nThese should be mapped to \"12th Pass\" or \"10th Pass\" as per the enum.\n\nNo years mentioned for high school and intermediate, so GraduationYear null.\n\nDiploma: \"Diploma in sports coaching, Gymnastics\" – matches \"Diploma in sports coaching\" in the enum? Not exactly, but closest might be general diploma or use the pattern.\n\nInstitution names: Need standardized. \"School Of Physical Education, DAVV, Indore\" – DAVV is Devi Ahilya Vishwavidyalaya, but schema says to use full standardized name, avoid abbreviations. So, perhaps \"Devi Ahilya Vishwavidyalaya\" or keep as is? The text uses \"DAVV\", but it says \"from School Of Physical Education, DAVV, Indore.\" I should use \"School of Physical Education, Devi Ahilya Vishwavidyalaya, Indore\" for consistency.\n\nSchema says: \"The full, standardized name of the educational institution. Avoid abbreviations or variants. For example, return 'Sage University' instead of 'SU'.\" So, expand DAVV to Devi Ahilya Vishwavidyalaya.\n\nSimilarly, for others.\n\nOrder: Highest to lowest. PhD is pursuing, so perhaps list completed degrees first.\n\nSchema note: \"Show Education in Highest to Lowest order, like PhD, MBA, MSc, BSc, 10th Pass, 12th Pass.\"\n\nPhD is not completed, so maybe only include completed degrees? But it's mentioned, so include with GraduationYear null.\n\nList:\n\n1. Pursuing PhD – degree not specified fully, \"PhD from mandsaur university\"\n\n2. Master of Physical Education, 2018\n\n3. Bachelor of Physical Education, 2015\n\n4. Diploma in sports coaching, 2018-19 – year range, perhaps graduation 2019\n\n5. Intermediate (12th Pass)\n\n6. High School (10th Pass)\n\nNo years for intermediate and high school.\n\nSpecialization: For degrees, e.g., Gymnastics for diploma.\n\nGPA and Percentage: Not mentioned, so GPA null, MaxGPA null, Percentage null.\n\n- WorkExperience: List in highest to lowest role order and latest first.\n\nFrom text:\n\n- Working as Physical education teacher in South valley International School, Betma, 2 years experience.\n\n- Working as Physical education teacher in Indore public school, Jhabua, 2 years.\n\n- Working as Physical education teacher in modern international school Indore, 3 years.\n\n- Worked as P.E.T (Internship) in sanmmati high secondary school, Indore, 45 days in 2017.\n\n- Worked as Gymnastics Coach (internship) in Indira Gandhi stadium, Delhi for 2 months in 2019.\n\nRoles: All are teacher or coach roles. Need to order by seniority or latest first.\n\nSchema says: \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern. as well as with latest work experience first\"\n\nSo, perhaps sort by end date descending, and within that, by role hierarchy.\n\nRoles are all similar: Physical education teacher or coach. Internship might be lower.\n\nLatest: Modern international school, 3 years – but no specific dates, only experience years. Assume current or recent.\n\nFrom text, \"Working as\" implies current, but multiple \"working as\" – probably past experiences listed.\n\nLook at years:\n\n- Modern international school: 3 year experience, state Madhya Pradesh.\n\n- Indore public school: 2 year, Jhabua.\n\n- South valley: 2 year, Betma.\n\n- Internship in 2017 and 2019.\n\nPerhaps the one with longest experience is older or newer? Not clear.\n\nList with latest end date first. Since \"working as\" might be current, but multiple, perhaps all past except one.\n\nAssume the order in text is chronological or not. Better to use dates where available.\n\n- Gymnastics Coach internship: 2019\n\n- P.E.T internship: 2017\n\n- Schools: no specific start/end, only experience years.\n\nFrom \"Participated in Leadership Training Camp 2013\", but that's not work.\n\nFor work experience, extract CompanyName, Role, StartYear, EndYear, Description.\n\nTotalWorkExperienceInYears: Sum of experiences. 2+2+3 = 7 years, plus internships might not count, or count as fractional. Schema says \"Total work experience.\", so probably sum the years mentioned. Internships have specific durations: 45 days and 2 months, which are less than a year, so perhaps not include in total years, or convert to years.\n\nTypically, total experience considers full-time roles. Here, the school jobs are mentioned with years, internships with months/days. Probably total from school jobs: 2+2+3=7 years.\n\n- Skills: From \"AREA OF SPECIAL INTEREST\" and \"AREA OF SPECIALIZATION\": Research in Physical education, Teaching physical education, Coaching in Artistic Gymnastics, Sports Biomechanics, Sports training. Also THEORY and SPORT specializations.\n\nList as array of strings: e.g., \"Research in Physical Education\", \"Teaching Physical Education\", etc.\n\n- Certifications: Not explicitly mentioned, but \"Diploma in sports coaching\" might be a certification. Also, various participations, but not certifications. The diploma is under education, but schema has separate Certifications.\n\nIn education, there's \"Diploma in sports coaching\", so perhaps it's already in education. Certifications might be other things like CA, but not mentioned. So, perhaps empty array.\n\n- Achievements: Many awards and participations listed, like medals in championships.\n\nExtract as list with AchievementName, IssuingOrganization, IssueDate.\n\nE.g., \"Secure Silver medal on high bar\", IssuingOrganization: \"Madhya Pradesh sub junior gymnastics championship\", Date: 2007-09-19 or something.\n\nNeed to parse the dates.\n\n- Languages: Not mentioned, so empty array.\n\n- Projects: Not mentioned, so empty array.\n\nNow, for embeddings: All embedding fields should be null as per instructions.\n\nReconstruct layout using coordinates to confirm groupings, but since the text is already somewhat structured by position, I can group by Y-coordinates or sections.\n\nFor example, on page 1, Y from 0.389 to 0.858 is education-related.\n\nTable-1 provides personal info directly.\n\nStart building the JSON.\n\nFirst, PersonalInformation:\n\n- FullName: \"Ankit Vyas\"\n\n- FullNameEmbedding: null\n\n- Gender: \"Male\" (inferred)\n\n- BirthDate: \"1995-06-10\" (from \"10.06.1995\", assuming DD.MM.YYYY)\n\n- Email: \"\" (not found)\n\n- Address: \"113, New Durga Colony Marimata Square, Indore\" (from Table-1)\n\n- City: \"Indore\" (from address)\n\n- State: \"Madhya Pradesh\" (from address and other mentions)\n\n- ContactNumber: \"9826765004\"\n\n- LinkedInProfile: \"\" (not found)\n\nObjective: \"\" (not found)\n\nEducation: Array, in order PhD > Masters > Bachelors > Diploma > 12th > 10th.\n\n1. PhD: Degree: \"PhD\", Specialization: perhaps \"Physical Education\" or null, Institution: \"Mandsaur University\", GraduationYear: null (pursuing), GPA: null, etc.\n\nText says \"Pursuing in PhD from mandsaur university, mandsaur(M.P.)\", no specialization mentioned.\n\n2. Master of Physical Education, 2018, Institution: \"School of Physical Education, Devi Ahilya Vishwavidyalaya, Indore\", Specialization: perhaps from area of interest, but not specified in degree, so maybe empty or infer.\n\nIn the degree section, it's just \"Master of Physical Education\", no spec. But in AREA OF SPECIALIZATION, it's \"Gymnastics in Physical Education\".\n\nFor the degree entry, Specialization might be null or \"Physical Education\".\n\nSchema says \"The specialization of the degree, if applicable.\", so for M.P.Ed., it might be general.\n\nSimilarly for B.P.Ed.\n\nDiploma: \"Diploma in sports coaching, Gymnastics\", so Specialization: \"Gymnastics\"\n\nInstitution for diploma: \"NSNIS, Patiala\" – expand to \"Netaji Subhas National Institute of Sports, Patiala\"?\n\nSchema says standardize, but not specified, so use as is or best effort. Perhaps \"NSNIS Patiala\".\n\nBut to be consistent, use full name if known. I know it's Netaji Subhas National Institute of Sports.\n\nFor high school and intermediate, use \"12th Pass\" and \"10th Pass\" as per enum.\n\nInstitution: for high school: \"Bal Niketan Sangh Pagnispaga High Secondary School\", for intermediate: \"Devi Ahilya Shishu Vihar High Secondary School, Indore\"\n\nNo years, so GraduationYear null.\n\nOrder: PhD (pursuing), then Masters 2018, Bachelors 2015, Diploma 2019, 12th Pass, 10th Pass.\n\nDiploma year: \"2018-19\", so probably graduated 2019.\n\nWorkExperience: \n\nCompanies: \n\n- South valley International School, Role: \"Physical education teacher\", Experience: 2 years. No specific dates.\n\n- Indore public school (Jhabua), Role: \"Physical education teacher\", 2 years.\n\n- Modern international school Indore, Role: \"Physical education teacher\", 3 years.\n\n- Internship as P.E.T in sanmmati high secondary school, 2017, 45 days.\n\n- Internship as Gymnastics Coach in Indira Gandhi stadium, 2019, 2 months.\n\nFor dates, where available: Internship 2017 and 2019.\n\nFor school jobs, no start/end, only duration. Assume they are full-time and calculate total experience.\n\nFor individual entries, StartYear and EndYear might be null or approximate.\n\nPerhaps use the experience years to infer. But better to leave StartYear and EndYear null where not specified.\n\nRole: \"Physical education teacher\" – schema says \"Must include the expertise and specialization Example: if taught Math then 'Math' should be included in the role.\" Here, it's physical education, so \"Physical Education Teacher\".\n\nFor internships, \"P.E.T (Internship)\" might be \"Physical Education Teacher Intern\", and \"Gymnastics Coach Intern\".\n\nDescription: From text, e.g., \"Working as Physical education teacher...\"\n\nTotalWorkExperienceInYears: Sum of 2+2+3=7, perhaps not including internships. Or include fractional. But schema likely expects numerical value, so 7.\n\nSkills: From AREA OF SPECIAL INTEREST and AREA OF SPECIALIZATION: \"Research in Physical education and sports\", \"Teaching physical education and sports\", \"Coaching in Artistic Gymnastics\", \"Sports Biomechanics\", \"Sports training\", \"Gymnastics in Physical Education\", \"Artistic Gymnastics\".\n\nList unique skills.\n\nCertifications: Perhaps the diploma could be here, but it's under education. No other certifications mentioned.\n\nAchievements: Many medal wins and participations. Extract as per schema.\n\nLanguages: Not mentioned.\n\nProjects: Not mentioned.\n\nNow, for dates, need to parse and format as \"YYYY-MM-DD\" or just year.\n\nSchema says format \"date\", so probably ISO format.\n\nFor example, BirthDate: \"10.06.1995\" -> \"1995-06-10\"\n\nFor education and work, similar.\n\nFor achievements, dates like \"19th to 20th Sept. 2007\" – perhaps use the event date, but schema has \"IssueDate\", so maybe the end date or approximate.\n\nUse the date mentioned.\n\nNow, build the JSON.\n\nFirst, ensure no duplicates and assign to correct categories.\n\nOutput must be a single JSON string."}}], "created": 1750944129, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 2359, "prompt_tokens": 7342, "total_tokens": 12988, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 3287, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4227, "text_tokens": 7342, "image_tokens": 0}, "num_sources_used": 0}}