import React, { useState, useEffect } from 'react';
import { FiUsers, FiSearch, FiDownload, <PERSON>Eye, FiUser, FiMapPin, FiRefreshCw, FiMail, FiPhone, FiHeart, FiCalendar, FiFilter, FiChevronDown } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ResumeModal from '../components/ResumeModal';
import { formatPhoneNumber } from '../utils/phoneFormatter';
import { formatName, formatJobTitle } from '../utils/textFormatter';
import './AllResumes.css';

const AllResumes = () => {
  const [resumes, setResumes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [error, setError] = useState(null);
  const [favorites, setFavorites] = useState({});

  // Filter states
  const [sortBy, setSortBy] = useState('name-asc'); // name-asc, name-desc, date-latest, date-earliest
  const [experienceFilter, setExperienceFilter] = useState('all'); // all, 0-2, 2-5, 5-10, 10+
  const [dateFilter, setDateFilter] = useState('all'); // all, today, week, month, custom
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [multipleFilters, setMultipleFilters] = useState(true); // Toggle between single and multiple filter modes

  // Function to transform MongoDB resume data to UI format
  const transformResumeData = (mongoResume) => {
    try {
      // Handle the nested Resume structure from MongoDB
      const resumeData = mongoResume.Resume || mongoResume;

      // Extract personal information
      const personalInfo = resumeData.PersonalInformation || {};
      const contactInfo = resumeData.ContactInformation || {};

      // Extract skills from various sections
      const skills = [];
      if (resumeData.Skills && Array.isArray(resumeData.Skills)) {
        skills.push(...resumeData.Skills);
      }
      if (resumeData.TechnicalSkills && Array.isArray(resumeData.TechnicalSkills)) {
        skills.push(...resumeData.TechnicalSkills);
      }

      // Extract work experience for title
      let title = 'Professional';
      if (resumeData.WorkExperience && Array.isArray(resumeData.WorkExperience) && resumeData.WorkExperience.length > 0) {
        title = resumeData.WorkExperience[0].Role || resumeData.WorkExperience[0].JobTitle || title;
      }

      // Get address - prioritize PersonalInformation.Address over ContactInformation.Address
      const address = personalInfo.Address || contactInfo.Address || 'Location not specified';

      // Extract experience information for card display
      let experienceDisplay = 'Not Mentioned';
      if (resumeData.TotalWorkExperienceInYears && resumeData.TotalWorkExperienceInYears > 0) {
        experienceDisplay = `${resumeData.TotalWorkExperienceInYears} years`;
      } else if (resumeData.WorkExperience && Array.isArray(resumeData.WorkExperience) && resumeData.WorkExperience.length > 0) {
        // Check if there's actual work experience data (not empty entries)
        const validExperience = resumeData.WorkExperience.filter(exp =>
          exp.CompanyName || exp.Role || exp.JobTitle || exp.StartYear
        );
        if (validExperience.length > 0) {
          experienceDisplay = `${validExperience.length}+ positions`;
        }
      }

      // Helper function to format education with specialization
      const formatEducationDisplay = (education) => {
        if (!education || !Array.isArray(education) || education.length === 0) {
          return 'Not Mentioned';
        }

        const firstEdu = education[0];
        if (!firstEdu) return 'Not Mentioned';

        let educationText = firstEdu.Degree || 'Degree';

        // Add specialization if available
        if (firstEdu.Specialization) {
          educationText += ` in ${firstEdu.Specialization}`;
        } else if (firstEdu.Field) {
          educationText += ` in ${firstEdu.Field}`;
        } else if (firstEdu.Subject) {
          educationText += ` in ${firstEdu.Subject}`;
        }

        // Remove institution from card display - only show degree and specialization
        return educationText;
      };

      // Extract education information for card display
      let educationDisplay = formatEducationDisplay(resumeData.Education);
      if (resumeData.Education && Array.isArray(resumeData.Education) && resumeData.Education.length > 0) {
        // Check if there's actual education data (not empty entries)
        const validEducation = resumeData.Education.filter(edu =>
          edu.Degree || edu.Institution || edu.GraduationYear
        );
        if (validEducation.length > 0) {
          // Get the highest/latest degree
          const latestEducation = validEducation[0];
          if (latestEducation.Degree && latestEducation.Institution) {
            educationDisplay = `${latestEducation.Degree} (${latestEducation.Institution})`;
          } else if (latestEducation.Degree) {
            educationDisplay = latestEducation.Degree;
          } else if (latestEducation.Institution) {
            educationDisplay = latestEducation.Institution;
          }
        }
      }

      // Format the timestamp for display
      const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'Not Available';

        try {
          // Handle different timestamp formats
          let date;
          if (timestamp.$date) {
            // MongoDB ISODate format
            date = new Date(timestamp.$date);
          } else if (typeof timestamp === 'string') {
            date = new Date(timestamp);
          } else if (timestamp instanceof Date) {
            date = timestamp;
          } else {
            return 'Not Available';
          }

          // Check if date is valid
          if (isNaN(date.getTime())) {
            return 'Not Available';
          }

          // Format as "Dec 6, 2024 at 1:30 PM"
          return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          }) + ' at ' + date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          });
        } catch (error) {
          console.error('Error formatting timestamp:', error);
          return 'Not Available';
        }
      };

      const addedDate = formatTimestamp(mongoResume.timestamp);

      return {
        id: mongoResume._id || Math.random().toString(36).substr(2, 9),
        name: personalInfo.FullName || personalInfo.Name || 'Unknown',
        title: title,
        location: address,
        email: personalInfo.Email || contactInfo.Email || '',
        phone: personalInfo.ContactNumber || personalInfo.Phone || contactInfo.Phone || '',
        skills: skills, // Include all skills for modal
        summary: resumeData.Summary || resumeData.Objective || 'No summary available',
        // Add fields expected by ResumeModal
        score: Math.floor(Math.random() * 30) + 70, // Random score between 70-100 for demo
        experience: resumeData.WorkExperience && resumeData.WorkExperience.length > 0
          ? resumeData.WorkExperience.map(exp => `${exp.Role || exp.JobTitle || 'Position'} at ${exp.CompanyName || 'Company'}`).join(', ')
          : 'No work experience listed',
        education: formatEducationDisplay(resumeData.Education),
        // New fields for card display
        experienceDisplay: experienceDisplay,
        educationDisplay: educationDisplay,
        addedDate: addedDate, // Add the formatted timestamp
        // Store the full resume data for modal (ResumeModal expects 'rawData')
        rawData: mongoResume,
        fullData: resumeData,
        originalData: mongoResume
      };
    } catch (error) {
      console.error('Error transforming resume data:', error);
      return {
        id: Math.random().toString(36).substr(2, 9),
        name: 'Error loading resume',
        title: 'Unknown',
        location: 'Unknown',
        email: '',
        phone: '',
        skills: [],
        summary: 'Error loading resume data',
        fullData: {},
        originalData: mongoResume
      };
    }
  };

  // Function to fetch all resumes from backend
  const fetchAllResumes = async (showToast = false) => {
    setLoading(true);
    setError(null);

    try {
      // Call the FastAPI backend /showall endpoint
      const response = await fetch('http://192.168.1.15:8002/showall');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        // Transform the MongoDB data to UI format
        const transformedResumes = data.map(transformResumeData);
        setResumes(transformedResumes);
        // Only show toast when explicitly requested (manual refresh)
        if (showToast) {
          toast.success(`Successfully loaded ${transformedResumes.length} resumes`);
        }
      } else {
        throw new Error('Invalid data format received from server');
      }
    } catch (error) {
      console.error('Error fetching resumes:', error);
      setError(error.message);
      toast.error(`Failed to load resumes: ${error.message}`);
      // Set empty array on error
      setResumes([]);
    } finally {
      setLoading(false);
    }
  };

  // Load favorites from localStorage
  const loadFavorites = () => {
    try {
      const savedFavorites = localStorage.getItem('favoriteResumes');
      if (savedFavorites) {
        const parsedFavorites = JSON.parse(savedFavorites);
        setFavorites(parsedFavorites);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites({});
    }
  };

  // Toggle favorite status
  const toggleFavorite = (resume) => {
    try {
      const currentFavorites = { ...favorites };
      const resumeId = resume.id;

      if (currentFavorites[resumeId]) {
        // Remove from favorites
        delete currentFavorites[resumeId];
        toast.success(`Removed ${resume.name} from favorites`);
      } else {
        // Add to favorites
        const favoriteData = {
          ...resume,
          dateAdded: new Date().toISOString(),
          rawData: resume.rawData || resume.originalData // Keep original data for download
        };
        currentFavorites[resumeId] = favoriteData;
        toast.success(`Added ${resume.name} to favorites`);
      }

      // Update state and localStorage
      setFavorites(currentFavorites);
      localStorage.setItem('favoriteResumes', JSON.stringify(currentFavorites));

      // Dispatch event to update header count
      window.dispatchEvent(new Event('favoritesUpdated'));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Error updating favorites');
    }
  };

  useEffect(() => {
    fetchAllResumes();
    loadFavorites();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handler functions
  const handleViewResume = (resume) => {
    setSelectedResume(resume);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const handleDownloadResume = async (resume) => {
    try {
      // Download the original PDF from MongoDB only
      const resumeId = resume._id || resume.id;

      if (!resumeId) {
        toast.error('Resume ID not found. Cannot download PDF.');
        return;
      }

      try {
        const response = await fetch(`http://192.168.1.15:8002/api/resumes/${resumeId}/download`);

        if (response.ok) {
          // PDF download successful
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // Get filename from response headers or use default
          const contentDisposition = response.headers.get('Content-Disposition');
          let filename = `${resume.name.replace(/\s+/g, '_')}_Resume.pdf`;

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1].replace(/['"]/g, '');
            }
          }

          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          toast.success(`Downloaded PDF for ${resume.name}`);
          return;
        } else {
          // Handle error response
          const errorData = await response.json();
          toast.error(`Failed to download PDF: ${errorData.error || 'Unknown error'}`);
          return;
        }
      } catch (pdfError) {
        console.error('PDF download failed:', pdfError);
        toast.error(`Failed to download PDF: ${pdfError.message}`);
        return;
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleRefresh = () => {
    fetchAllResumes(true); // Show toast on manual refresh
  };

  const handleDeleteResume = (resumeId) => {
    // Remove the deleted resume from the local state
    setResumes(prevResumes => prevResumes.filter(resume => resume.id !== resumeId));
    // Optionally refresh the data from server to ensure consistency
    setTimeout(() => {
      fetchAllResumes(false); // Refresh without toast
    }, 1000);
  };

  // Helper function to extract experience years from resume data
  const getExperienceYears = (resume) => {
    if (resume.fullData?.TotalWorkExperienceInYears) {
      return parseInt(resume.fullData.TotalWorkExperienceInYears);
    }

    // Try to extract from experience display text
    const expText = resume.experienceDisplay || '';
    const match = expText.match(/(\d+)/);
    if (match) {
      return parseInt(match[1]);
    }

    return 0;
  };

  // Helper function to parse date from addedDate string
  const parseAddedDate = (dateString) => {
    if (!dateString || dateString === 'Not Available') return null;

    try {
      // Parse "Dec 6, 2024 at 1:30 PM" format
      const parts = dateString.split(' at ');
      if (parts.length === 2) {
        return new Date(parts[0] + ' ' + parts[1]);
      }
      return new Date(dateString);
    } catch (error) {
      return null;
    }
  };

  // Generate filter context text
  const getFilterContext = () => {
    const contexts = [];

    // Add sorting context
    if (sortBy === 'name-asc') {
      contexts.push('Sorted by Name (A-Z)');
    } else if (sortBy === 'name-desc') {
      contexts.push('Sorted by Name (Z-A)');
    } else if (sortBy === 'date-latest') {
      contexts.push('Sorted by Date (Latest First)');
    } else if (sortBy === 'date-earliest') {
      contexts.push('Sorted by Date (Earliest First)');
    }

    // Add experience filter context
    if (experienceFilter !== 'all') {
      const expLabels = {
        '0-2': '0-2 years experience',
        '2-5': '2-5 years experience',
        '5-10': '5-10 years experience',
        '10+': '10+ years experience'
      };
      contexts.push(`Filtered by ${expLabels[experienceFilter]}`);
    }

    // Add date filter context
    if (dateFilter !== 'all') {
      const dateLabels = {
        'today': 'Added today',
        'week': 'Added in last week',
        'month': 'Added in last month',
        'custom': customDateFrom && customDateTo ?
          `Added between ${new Date(customDateFrom).toLocaleDateString()} and ${new Date(customDateTo).toLocaleDateString()}` :
          'Custom date range'
      };
      contexts.push(`Filtered by ${dateLabels[dateFilter]}`);
    }

    return contexts;
  };

  // Filter and sort resumes
  const getFilteredAndSortedResumes = () => {
    let filtered = resumes.filter(resume =>
      resume.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resume.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resume.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    // In single filter mode, only apply the most recently changed filter
    if (!multipleFilters) {
      // Apply only one filter based on what was last changed
      // For now, we'll prioritize in order: experience, date, then sort
      if (experienceFilter !== 'all') {
        filtered = filtered.filter(resume => {
          const years = getExperienceYears(resume);
          switch (experienceFilter) {
            case '0-2': return years >= 0 && years <= 2;
            case '2-5': return years > 2 && years <= 5;
            case '5-10': return years > 5 && years <= 10;
            case '10+': return years > 10;
            default: return true;
          }
        });
      } else if (dateFilter !== 'all') {
        const now = new Date();
        filtered = filtered.filter(resume => {
          const resumeDate = parseAddedDate(resume.addedDate);
          if (!resumeDate) return false;

          switch (dateFilter) {
            case 'today':
              return resumeDate.toDateString() === now.toDateString();
            case 'week':
              const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              return resumeDate >= weekAgo;
            case 'month':
              const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
              return resumeDate >= monthAgo;
            case 'custom':
              if (customDateFrom && customDateTo) {
                const fromDate = new Date(customDateFrom);
                const toDate = new Date(customDateTo);
                return resumeDate >= fromDate && resumeDate <= toDate;
              }
              return true;
            default:
              return true;
          }
        });
      }
    } else {
      // Multiple filters mode - apply all filters
      // Apply experience filter
      if (experienceFilter !== 'all') {
        filtered = filtered.filter(resume => {
          const years = getExperienceYears(resume);
          switch (experienceFilter) {
            case '0-2': return years >= 0 && years <= 2;
            case '2-5': return years > 2 && years <= 5;
            case '5-10': return years > 5 && years <= 10;
            case '10+': return years > 10;
            default: return true;
          }
        });
      }

      // Apply date filter
      if (dateFilter !== 'all') {
        const now = new Date();
        filtered = filtered.filter(resume => {
          const resumeDate = parseAddedDate(resume.addedDate);
          if (!resumeDate) return false;

          switch (dateFilter) {
            case 'today':
              return resumeDate.toDateString() === now.toDateString();
            case 'week':
              const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              return resumeDate >= weekAgo;
            case 'month':
              const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
              return resumeDate >= monthAgo;
            case 'custom':
              if (customDateFrom && customDateTo) {
                const fromDate = new Date(customDateFrom);
                const toDate = new Date(customDateTo);
                return resumeDate >= fromDate && resumeDate <= toDate;
              }
              return true;
            default:
              return true;
          }
        });
      }
    }

    // Apply sorting (always applied regardless of filter mode)
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'date-latest':
          const dateA = parseAddedDate(a.addedDate);
          const dateB = parseAddedDate(b.addedDate);
          if (!dateA && !dateB) return 0;
          if (!dateA) return 1;
          if (!dateB) return -1;
          return dateB.getTime() - dateA.getTime();
        case 'date-earliest':
          const dateA2 = parseAddedDate(a.addedDate);
          const dateB2 = parseAddedDate(b.addedDate);
          if (!dateA2 && !dateB2) return 0;
          if (!dateA2) return 1;
          if (!dateB2) return -1;
          return dateA2.getTime() - dateB2.getTime();
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredResumes = getFilteredAndSortedResumes();

  if (loading) {
    return (
      <div className="all-resumes-page">
        <div className="loading-container">
          <div className="loading-spinner large" />
          <p>Loading resumes from database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="all-resumes-page">
      <div className="page-header">
        <h1>All Resumes</h1>
        <p>Browse and manage your complete resume database</p>
      </div>

      <div className="controls-section">
        <div className="search-control">
          <FiSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search resumes by name, title, or skills..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        <div className="controls-right">
          <button
            className="btn btn-tertiary filter-toggle-btn"
            onClick={() => setShowFilters(!showFilters)}
            title="Toggle filters"
          >
            <FiFilter size={16} />
            Filters
            <FiChevronDown
              size={14}
              className={`filter-chevron ${showFilters ? 'rotated' : ''}`}
            />
          </button>
          <button
            className="btn btn-secondary refresh-btn"
            onClick={handleRefresh}
            disabled={loading}
            title="Refresh resumes from database"
          >
            <FiRefreshCw size={16} />
            Refresh
          </button>
          <div className="stats">
            <FiUsers size={16} />
            <span>{filteredResumes.length} of {resumes.length} resumes</span>
          </div>
        </div>
      </div>

      {/* Filter Section */}
      {showFilters && (
        <div className="filter-panel">
          {/* Filter Mode Toggle */}
          <div className="filter-mode-section">
            <div className="filter-mode-toggle">
              <label className="filter-mode-label">Filter Mode:</label>
              <div className="toggle-buttons">
                <button
                  className={`toggle-btn ${multipleFilters ? 'active' : ''}`}
                  onClick={() => setMultipleFilters(true)}
                >
                  Multiple Filters
                </button>
                <button
                  className={`toggle-btn ${!multipleFilters ? 'active' : ''}`}
                  onClick={() => setMultipleFilters(false)}
                >
                  Single Filter
                </button>
              </div>
            </div>
          </div>

          <div className="filter-row">
            <div className="filter-group">
              <label className="filter-label">Sort by Name</label>
              <select
                value={sortBy.startsWith('name') ? sortBy : 'name-asc'}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="name-asc">A to Z</option>
                <option value="name-desc">Z to A</option>
              </select>
            </div>

            <div className="filter-group">
              <label className="filter-label">Sort by Date</label>
              <select
                value={sortBy.startsWith('date') ? sortBy : 'date-latest'}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="date-latest">Latest First</option>
                <option value="date-earliest">Earliest First</option>
              </select>
            </div>

            <div className="filter-group">
              <label className="filter-label">Experience</label>
              <select
                value={experienceFilter}
                onChange={(e) => setExperienceFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Experience</option>
                <option value="0-2">0-2 years</option>
                <option value="2-5">2-5 years</option>
                <option value="5-10">5-10 years</option>
                <option value="10+">10+ years</option>
              </select>
            </div>

            <div className="filter-group">
              <label className="filter-label">Date Added</label>
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Dates</option>
                <option value="today">Today</option>
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>
          </div>

          {/* Custom Date Range */}
          {dateFilter === 'custom' && (
            <div className="filter-row custom-date-row">
              <div className="filter-group">
                <label className="filter-label">From Date</label>
                <input
                  type="date"
                  value={customDateFrom}
                  onChange={(e) => setCustomDateFrom(e.target.value)}
                  className="filter-date-input"
                />
              </div>
              <div className="filter-group">
                <label className="filter-label">To Date</label>
                <input
                  type="date"
                  value={customDateTo}
                  onChange={(e) => setCustomDateTo(e.target.value)}
                  className="filter-date-input"
                />
              </div>
            </div>
          )}

          {/* Filter Actions */}
          <div className="filter-actions">
            <button
              className="btn btn-secondary"
              onClick={() => {
                setSortBy('name-asc');
                setExperienceFilter('all');
                setDateFilter('all');
                setCustomDateFrom('');
                setCustomDateTo('');
              }}
            >
              Reset Filters
            </button>
          </div>
        </div>
      )}

      {/* Filter Context Display */}
      {getFilterContext().length > 0 && (
        <div className="filter-context">
          <div className="filter-context-header">
            <span className="context-label">Active Filters:</span>
            <span className="filter-mode-indicator">
              ({multipleFilters ? 'Multiple' : 'Single'} Filter Mode)
            </span>
          </div>
          <div className="filter-context-tags">
            {getFilterContext().map((context, index) => (
              <span key={index} className="filter-tag">
                {context}
              </span>
            ))}
          </div>
        </div>
      )}

      {error && (
        <div className="error-message">
          <p>Error loading resumes: {error}</p>
          <button className="btn btn-primary" onClick={handleRefresh}>
            Try Again
          </button>
        </div>
      )}

      {!loading && !error && resumes.length === 0 && (
        <div className="empty-state">
          <FiUsers size={48} />
          <h3>No resumes found</h3>
          <p>No resumes are currently stored in the database.</p>
          <button className="btn btn-primary" onClick={handleRefresh}>
            Refresh
          </button>
        </div>
      )}

      {filteredResumes.length > 0 && (
        <div className="resumes-grid">
          {filteredResumes.map(resume => (
            <div key={resume.id} className="resume-card">
              <button
                className={`favorite-btn ${favorites[resume.id] ? 'favorited' : ''}`}
                onClick={() => toggleFavorite(resume)}
                title={favorites[resume.id] ? 'Remove from favorites' : 'Add to favorites'}
              >
                <FiHeart size={16} />
              </button>
              <div className="resume-avatar">
                <FiUser size={24} />
              </div>
              <h3>{formatName(resume.name)}</h3>
              <p className="title">{formatJobTitle(resume.title)}</p>
              <div className="location">
                <FiMapPin size={14} />
                <span>{resume.location}</span>
              </div>

              {/* Contact Information */}
              <div className="contact-info">
                <div className="contact-item">
                  <FiMail size={14} />
                  <span>{resume.email || 'Not Mentioned'}</span>
                </div>
                <div className="contact-item">
                  <FiPhone size={14} />
                  <span>{formatPhoneNumber(resume.phone)}</span>
                </div>
              </div>

              {/* Experience Section */}
              <div className="resume-detail-section">
                <h4 className="detail-label">Experience</h4>
                <p className="detail-value">{resume.experienceDisplay}</p>
              </div>

              {/* Education Section */}
              <div className="resume-detail-section">
                <h4 className="detail-label">Education</h4>
                <p className="detail-value">{resume.educationDisplay}</p>
              </div>

              {/* Skills Section */}
              <div className="resume-detail-section">
                <h4 className="detail-label">Skills</h4>
                <div className="skills">
                  {resume.skills.length > 0 ? (
                    resume.skills.map((skill, index) => (
                      <span key={index} className="skill-tag">{skill}</span>
                    ))
                  ) : (
                    <span className="no-skills">Not Mentioned</span>
                  )}
                </div>
              </div>

              {/* Added Date Section */}
              <div className="resume-detail-section">
                <h4 className="detail-label">Added</h4>
                <div className="added-date-container">
                  <FiCalendar size={12} />
                  <span className="added-date">{resume.addedDate}</span>
                </div>
              </div>

              <div className="actions">
                <button
                  className="btn btn-secondary"
                  onClick={() => handleViewResume(resume)}
                  title={`View ${resume.name}'s resume`}
                >
                  <FiEye size={16} />
                  View
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => handleDownloadResume(resume)}
                  title={`Download ${resume.name}'s resume`}
                >
                  <FiDownload size={16} />
                  Download
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Resume Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onDelete={handleDeleteResume}
      />
    </div>
  );
};

export default AllResumes;
