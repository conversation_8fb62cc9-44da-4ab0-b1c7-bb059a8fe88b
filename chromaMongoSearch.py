"""
ChromaDB + MongoDB Hybrid Search System

This module implements a two-stage search process:
1. MongoDB search excluding embedding factors to get MongoDB IDs
2. ChromaDB vector search on those specific MongoDB IDs using OpenAI embeddings
3. Return results with match distances

Usage:
    from chromaMongoSearch import ChromaMongoSearchEngine
    
    search_engine = ChromaMongoSearchEngine()
    results = await search_engine.search("Find software engineers with Python experience")
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple, Set
from openai import OpenAI
from chromadb import HttpClient
from helperMongoDb import MongoDBClient
from bson import ObjectId
import asyncio

class ChromaMongoSearchEngine:
    """
    Hybrid search engine that combines MongoDB filtering with ChromaDB vector search.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2_chroma",
                 chroma_host: str = "localhost",
                 chroma_port: int = 8001,
                 chroma_collection: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the hybrid search engine.
        
        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            chroma_collection: ChromaDB collection name
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
        """
        self.database_name = database_name
        self.collection_name = collection_name
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize MongoDB client
        self.mongo_client = MongoDBClient(db_name=database_name)
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.chroma_collection = self.chroma_client.get_or_create_collection(chroma_collection)

        # how many neighbors you want per condition
        self.max_matches_per_condition = 50

        # how “tight” your semantic match needs to be
        self.distance_threshold = 5
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            # Use default OpenAI configuration
            self.openai_client = OpenAI()
        
        # Define embedding columns for vector search (15 total as requested)
        self.embedding_columns = [
            "FullNameEmbedding",
            "InstitutionEmbedding",
            "CompanyNameEmbedding",
            "RoleEmbedding",
            "Description/ResponsibilityEmbedding",
            "SkillsEmbedding",
            "CertificationNameEmbedding",
            "IssuingOrganizationEmbedding",
            "AchievementNameEmbedding",
            "ProjectNameEmbedding",
            "DescriptionEmbedding",
            "TechnologiesUsedEmbedding",
            "ProjectRoleEmbedding"
        ]

        # Map embedding columns to their corresponding embedding types in ChromaDB
        self.embedding_type_mapping = {
            "FullNameEmbedding": "FullName",
            "InstitutionEmbedding": "Institution",
            "CompanyNameEmbedding": "CompanyName",
            "RoleEmbedding": "Role",
            "Description/ResponsibilityEmbedding": "Description/Responsibility",
            "SkillsEmbedding": "Skills",
            "CertificationNameEmbedding": "CertificationName",
            "IssuingOrganizationEmbedding": "IssuingOrganization",
            "AchievementNameEmbedding": "AchievementName",
            "ProjectNameEmbedding": "ProjectName",
            "DescriptionEmbedding": "Description",
            "TechnologiesUsedEmbedding": "TechnologiesUsed",
            "ProjectRoleEmbedding": "ProjectRole"
        }
        
        # Define fields to exclude from MongoDB search (corresponding to embedding columns)
        self.excluded_mongodb_fields = [
            "Resume.PersonalInformation.FullName",
            "Resume.Education.Institution",
            "Resume.WorkExperience.CompanyName", 
            "Resume.WorkExperience.Role",
            "Resume.WorkExperience.Description/Responsibility",
            "Resume.Skills",
            "Resume.Certifications.CertificationName",
            "Resume.Certifications.IssuingOrganization",
            "Resume.Achievements.AchievementName",
            "Resume.Projects.ProjectName",
            "Resume.Projects.Description",
            "Resume.Projects.TechnologiesUsed",
            "Resume.Projects.Role"
        ]

    def set_distance_threshold(self, threshold: float):
        """
        Set the distance threshold for semantic matching.

        Args:
            threshold: Distance threshold value (0.5 for exact match, 2.0 for similar match)
        """
        self.distance_threshold = threshold
        print(f"🎯 Distance threshold set to: {threshold}")

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            # Normalize text to lowercase for consistent embeddings
            text = text.lower()
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None



    def get_chromadb_docs_by_mongodb_ids(self, mongodb_ids: List[str]) -> Dict[str, Any]:
        """
        Get ChromaDB documents filtered by MongoDB IDs, focusing only on the 13 embedding fields.

        Args:
            mongodb_ids: List of MongoDB IDs to filter by

        Returns:
            ChromaDB query results filtered to only the 13 embedding types
        """
        try:
            if not mongodb_ids:
                return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

            # Define the 13 embedding types we want to search (without "Embedding" suffix)
            target_embedding_types = [
                "FullName",
                "Institution",
                "CompanyName",
                "Role",
                "Description/Responsibility",
                "Skills",
                "CertificationName",
                "IssuingOrganization",
                "AchievementName",
                "ProjectName",
                "Description",
                "TechnologiesUsed",
                "ProjectRole"
            ]

            # Query ChromaDB for documents with these MongoDB IDs AND specific embedding types
            results = self.chroma_collection.get(
                where={
                    "$and": [
                        {"mongodb_id": {"$in": mongodb_ids}},
                        {"embedding_type": {"$in": target_embedding_types}}
                    ]
                },
                include=["metadatas", "documents", "embeddings"]
            )

            self.logger.info(f"Found {len(results.get('ids', []))} ChromaDB documents for {len(mongodb_ids)} MongoDB IDs (13 embedding types only)")
            return results

        except Exception as e:
            self.logger.error(f"Error querying ChromaDB: {e}")
            return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

    async def generate_and_execute_mongodb_query(self, user_query: str, limit: int = 1000) -> List[str]:
        """
        Generate MongoDB query using OpenAI and execute it directly.

        Args:
            user_query: Natural language query from user
            limit: Maximum number of results to return

        Returns:
            List of MongoDB IDs as strings
        """
        try:
            print(f"\n🤖 GENERATING MONGODB QUERY WITH OPENAI...")
            print(f"   User Query: '{user_query}'")

            # OpenAI call to generate MongoDB query
            system_prompt = """
            You are a MongoDB query generator for resume search. Your task is to convert a natural language query
            into a MongoDB query that searches ONLY on non-embedding fields.

            Available non-embedding fields to search:
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages

            DO NOT search on these embedding fields (they will be handled separately):
            - FullName, Institution, CompanyName, Role, Description/Responsibility
            - Skills, CertificationName, IssuingOrganization, AchievementName
            - ProjectName, Description, TechnologiesUsed, ProjectRole

            Restrictions:
            - Only cities from the following list are valid for Resume.PersonalInformation.City filtering: [ "New York", "Los Angeles", "Chicago", "San Francisco", "Houston", "Miami", "Boston", "Washington D.C.",
                    "London", "Paris", "Berlin", "Madrid", "Rome", "Barcelona", "Amsterdam", "Brussels", "Vienna", "Stockholm",
                    "Copenhagen", "Oslo", "Helsinki", "Zurich", "Tokyo", "Osaka", "Beijing", "Shanghai", "Shenzhen", "Guangzhou",
                    "Seoul", "Hong Kong", "Singapore", "Bangkok", "Jakarta", "Manila", "Dubai", "Abu Dhabi", "Doha", "Riyadh",
                    "Jeddah", "Tehran", "Baghdad", "Cairo", "Lagos", "Nairobi", "Johannesburg", "Cape Town", "São Paulo",
                    "Rio de Janeiro", "Buenos Aires", "Lima", "Bogotá", "Mexico City", "Toronto", "Vancouver", "Montreal",
                    "Sydney", "Melbourne", "Auckland", "Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata",
                    "Pune", "Ahmedabad", "Surat", "Jaipur", "Lucknow", "Kanpur", "Nagpur", "Visakhapatnam", "Indore", "Bhopal",
                    "Patna", "Ludhiana", "Agra", "Varanasi", "Coimbatore", "Vadodara", "Nashik", "Faridabad", "Meerut", "Rajkot",
                    "Amritsar", "Allahabad (Prayagraj)", "Ranchi", "Guwahati", "Chandigarh", "Jodhpur", "Madurai", "Raipur",
                    "Kochi", "Jamshedpur", "Thiruvananthapuram", "Dehradun", "Jabalpur", "Gwalior", "Ujjain", "Sagar", "Dewas",
                    "Satna", "Rewa", "Ratlam", "Katni", "Khandwa", "Chhindwara", "Vidisha", "Shivpuri", "Damoh", "Morena",
                    "Hoshangabad", "Itarsi", "Betul", "Sehore" 
                ]
            - Only states from the following list are valid for Resume.PersonalInformation.State filtering: [
                    "California", "Texas", "Florida", "New York", "Illinois", "Pennsylvania", "Ohio",
                    "Uttar Pradesh", "Maharashtra", "Bihar", "West Bengal", "Tamil Nadu", "Karnataka", "Gujarat",
                    "Rajasthan", "Madhya Pradesh", "Andhra Pradesh", "Kerala", "Odisha", "Punjab", "Haryana",
                    "Telangana", "Assam", "Chhattisgarh", "Jharkhand",
                    "Guangdong", "Shandong", "Henan", "Sichuan", "Jiangsu", "Hebei", "Hunan", "Anhui",
                    "Zhejiang", "Yunnan", "Beijing", "Shanghai",
                    "Ontario", "Quebec", "British Columbia", "Alberta",
                    "New South Wales", "Victoria", "Queensland", "Western Australia",
                    "São Paulo", "Minas Gerais", "Rio de Janeiro", "Bahia", "Paraná",
                    "Moscow", "Saint Petersburg", "Krasnodar Krai", "Tatarstan", "Sverdlovsk Oblast",
                    "Bavaria", "North Rhine-Westphalia", "Baden-Württemberg", "Berlin", "Hesse",
                    "Mexico City", "Jalisco", "Nuevo León", "Estado de México",
                    "Gauteng", "KwaZulu-Natal", "Western Cape",
                    "Lagos", "Kano", "Kaduna", "Rivers",
                    "Java", "Sumatra", "Bali", "Kalimantan",
                    "England", "Scotland", "Wales", "Northern Ireland"
                ]
            - Only degree from the following list are valid for Resume.Education.Degree filtering: ["10th Pass", "12th Pass", 

                    "BA (Bachelor of Arts)", "BSc (Bachelor of Science)", "BCom (Bachelor of Commerce)",
                    "BBA (Bachelor of Business Administration)", "BCA (Bachelor of Computer Applications)",
                    "BTech (Bachelor of Technology)", "BE (Bachelor of Engineering)",
                    "LLB (Bachelor of Laws)", "MBBS (Bachelor of Medicine and Bachelor of Surgery)",
                    "BPharm (Bachelor of Pharmacy)", "BArch (Bachelor of Architecture)",
                    "BDS (Bachelor of Dental Surgery)", "BFA (Bachelor of Fine Arts)",
                    "BHM (Bachelor of Hotel Management)", "BPT (Bachelor of Physiotherapy)",
                    "BSc IT (Bachelor of Science in Information Technology)", "BDes (Bachelor of Design)",
                    "BMM (Bachelor of Mass Media)", "BJMC (Bachelor of Journalism and Mass Communication)",
                    "BLib (Bachelor of Library Science)", "BPEd (Bachelor of Physical Education)",
                    "BSc Nursing (Bachelor of Science in Nursing)", "BEd (Bachelor of Education)",

                    "BA LLB (Bachelor of Arts and Bachelor of Laws)", "BBA LLB (Bachelor of Business Administration and Bachelor of Laws)",

                    "MA (Master of Arts)", "MSc (Master of Science)", "MCom (Master of Commerce)",
                    "MBA (Master of Business Administration)", "MCA (Master of Computer Applications)",
                    "MTech (Master of Technology)", "ME (Master of Engineering)",
                    "LLM (Master of Laws)", "MPharm (Master of Pharmacy)", "MFA (Master of Fine Arts)",
                    "MDS (Master of Dental Surgery)", "MHM (Master of Hotel Management)",
                    "MPT (Master of Physiotherapy)", "MS (Master of Surgery)", "MLib (Master of Library Science)",
                    "MPEd (Master of Physical Education)", "MSc Nursing (Master of Science in Nursing)", "MEd (Master of Education)", 
                    "MJMC (Master of Journalism and Mass Communication)", "MDes (Master of Design)",

                    "PhD (Doctor of Philosophy)", "DSc (Doctor of Science)", "DLitt (Doctor of Literature)",

                    "BEd (Bachelor of Education)", 
                    "MEd (Master of Education)",
                    "D.El.Ed (Diploma in Elementary Education)",
                    "B.El.Ed (Bachelor of Elementary Education)",
                    "NTT (Nursery Teacher Training)",
                    "PTT (Primary Teacher Training)",
                    "TTC (Teacher Training Certificate)",
                    "CTET (Central Teacher Eligibility Test)",
                    "TET (Teacher Eligibility Test)",
                    "UPTET (Uttar Pradesh Teacher Eligibility Test)",
                    "MPTET (Madhya Pradesh Teacher Eligibility Test)",
                    "HTET (Haryana Teacher Eligibility Test)",
                    "KTET (Kerala Teacher Eligibility Test)",
                    "OTET (Odisha Teacher Eligibility Test)",
                    "AP TET (Andhra Pradesh Teacher Eligibility Test)",
                    "KVS Exam (Kendriya Vidyalaya Sangathan Recruitment)",
                    "NVS Exam (Navodaya Vidyalaya Samiti Recruitment)",
                    "STET (State Teacher Eligibility Test)",
                    "UGC NET (University Grants Commission National Eligibility Test)",
                    "SET (State Eligibility Test)",
                    "IGNOU BEd (Indira Gandhi National Open University BEd Program)",
                    "IGNOU MEd (Indira Gandhi National Open University MEd Program)",
                    "PGCE (Postgraduate Certificate in Education - UK)",
                    "TEFL (Teaching English as a Foreign Language)",
                    "TESOL (Teaching English to Speakers of Other Languages)",
                    "CELTA (Certificate in English Language Teaching to Adults)",
                    "Montessori Training (Early Childhood Education Certification)",
                    "IB Certification (International Baccalaureate Teacher Training)",
                    "WBT (Waldorf Basic Training)",
                    "DELTA (Diploma in Teaching English to Speakers of Other Languages - Cambridge)",

                    "CA (Chartered Accountant)", "CS (Company Secretary)", "CMA (Cost and Management Accountant)",
                    "CPA (Certified Public Accountant)", "CFA (Chartered Financial Analyst)",

                    "ITI (Industrial Training Institute)", "DCA (Diploma in Computer Applications)",
                    "PGDCA (Post Graduate Diploma in Computer Applications)", "DPharm (Diploma in Pharmacy)",
                    "GNM (General Nursing and Midwifery)", "ANM (Auxiliary Nurse Midwifery)",
                    "DOEACC O Level (Foundation Diploma in Computer Science)", "DOEACC A Level (Advanced Diploma in Computer Applications)", 
                    
                    "BStat (Bachelor of Statistics)", "MStat (Master of Statistics)"
                ]
                - Only Specialization from the following list are valid for Resume.Education.Specialization filtering: [
                    "Artificial Intelligence", "Machine Learning", "Data Science", "Cybersecurity", "Blockchain", "Web Development", "Mobile App Development", "Cloud Computing", "Information Technology", "Software Engineering", "Computer Networks", "Big Data Analytics", "Computer Applications",
                    "Mechanical Engineering", "Civil Engineering", "Electrical Engineering", "Electronics and Communication", "Computer Engineering", "Automobile Engineering", "Mechatronics", "Aerospace Engineering", "Chemical Engineering", "Biotechnology", "Environmental Engineering", "Petroleum Engineering",
                    "Physics", "Chemistry", "Mathematics", "Zoology", "Botany", "Biochemistry", "Microbiology", "Genetics", "Environmental Science", "Forensic Science", "Agricultural Science",
                    "Accounting and Finance", "Marketing", "Human Resource Management", "International Business", "Banking and Insurance", "Business Analytics", "Operations Management", "Entrepreneurship", "Supply Chain Management", "E-Commerce",
                    "English Literature", "History", "Political Science", "Sociology", "Psychology", "Philosophy", "Economics", "Geography", "Public Administration", "Linguistics",
                    "Corporate Law", "Criminal Law", "International Law", "Cyber Law", "Intellectual Property Law", "Environmental Law", "Constitutional Law", "Labour Law",
                    "General Medicine", "Surgery", "Dentistry", "Pharmacy", "Nursing", "Physiotherapy", "Radiology", "Pathology", "Gynecology", "Pediatrics", "Dermatology", "Psychiatry", "Public Health", "Ayurveda", "Homeopathy",
                    "Graphic Design", "Fashion Design", "Interior Design", "UI/UX Design", "Animation and VFX", "Photography", "Film Making", "Mass Communication", "Journalism", "Advertising",
                    "Horticulture", "Forestry", "Dairy Technology", "Fisheries Science", "Agronomy", "Soil Science", "Plant Pathology", 
                    "Early Childhood Education", "Elementary Education", "Secondary Education", "Special Education", "Educational Psychology", "Curriculum and Instruction", "Counseling and Guidance", "Educational Leadership and Administration", "Higher Education", "Adult and Continuing Education", "Teacher Training and Pedagogy", "Language Education (English, Hindi, Regional Languages)", "Mathematics Education", "Science Education", "Social Science Education", "Physical Education", "Inclusive Education", "Educational Technology", "Assessment and Evaluation in Education", "Vocational Education", "Moral and Value Education"
                ]
                - Only languages from the following list are valid for Resume.Languages filtering: [
                    "English", "Mandarin Chinese", "Spanish", "Arabic", "Portuguese", "Russian", "Japanese", "German", "French", "Turkish", "Korean", "Vietnamese", "Persian (Farsi)", "Malay/Indonesian", "Thai", "Polish", "Ukrainian", "Dutch", "Romanian", "Swahili", "Hindi", "Bengali", "Telugu", "Marathi", "Tamil", "Urdu", "Gujarati", "Kannada", "Odia", "Malayalam", "Punjabi", "Assamese", "Maithili", "Santali", "Kashmiri", "Nepali", "Konkani", "Dogri", "Manipuri", "Sindhi", "Bodo", "Santhali", "Sanskrit"
                ]
                
            For the following fields — Resume.PersonalInformation.State, Resume.PersonalInformation.City, Resume.Education.Degree, Resume.Education.Specialization, and Resume.Languages — you MUST return values only from their corresponding predefined valid lists.
            When the user's input uses informal or shorthand terms (e.g., "done MBA", "from Bombay", "taught kids in UP"), you MUST:
            - Map the input to the closest valid match from the list, e.g.:
                - "done MBA" → "MBA (Master of Business Administration)"
                - "bombay" → "Mumbai"
                - "UP" → "Uttar Pradesh"
            Ignore or omit any value that cannot be confidently mapped to a valid entry.

            Return a valid MongoDB query in JSON format. Use $regex for text matching and $and/$or for combinations.

            Examples:
            - "Find females" -> {"Resume.PersonalInformation.Gender": {"$regex": "^female$", "$options": "i"}}
            - "Teachers with degree" -> {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}
            - "5+ years experience" -> {"Resume.TotalWorkExperienceInYears": {"$gte": 5}
            - "People from Mumbai" -> {"Resume.PersonalInformation.City": {"$regex": "mumbai", "$options": "i"}}
            - "Female teachers from Delhi with 3+ years experience" -> {"$and": [{"Resume.PersonalInformation.Gender": {"$regex": "^female$", "$options": "i"}}, {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}, {"Resume.PersonalInformation.City": {"$regex": "delhi", "$options": "i"}}, {"Resume.TotalWorkExperienceInYears": {"$gte": 3}}]}

            NOTE: Do not consider Resume.Education.Degree when the query is like "show me a female who worked a physical education", that's the Role not the Degree.

            If no non-embedding fields are mentioned, return an empty object {}.
            Return ONLY valid JSON, no explanations.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate MongoDB query for: {user_query}"}
                ],
                temperature=0.1
            )

            query_text = response.choices[0].message.content.strip()
            print(f"   🔍 OpenAI Response: {query_text}")

            # Parse the JSON query
            try:
                mongodb_query = json.loads(query_text)
                print(f"   ✅ Parsed MongoDB Query: {mongodb_query}")
            except json.JSONDecodeError:
                print(f"   ❌ Failed to parse JSON, using empty query")
                mongodb_query = {}

            # Execute the MongoDB query
            print(f"\n🗄️  EXECUTING MONGODB QUERY...")
            collection = self.mongo_client.db[self.collection_name]

            # Use the generated MongoDB query, or fallback to all documents
            search_filter = mongodb_query if mongodb_query else {}
            print(f"   Query Filter: {search_filter}")

            # Get documents but only return the _id field
            cursor = collection.find(
                search_filter,
                {"_id": 1}
            ).limit(limit)

            mongodb_ids = [str(doc["_id"]) for doc in cursor]

            print(f"   📊 MongoDB Results: {len(mongodb_ids)} IDs found")
            if len(mongodb_ids) > 0:
                print(f"   📋 Sample IDs: {mongodb_ids[:3]}...")

            self.logger.info(f"Generated and executed MongoDB query: {search_filter}")
            self.logger.info(f"Found {len(mongodb_ids)} MongoDB IDs")

            return mongodb_ids, search_filter

        except Exception as e:
            print(f"   ❌ Error in MongoDB query generation/execution: {e}")
            self.logger.error(f"Error in generate_and_execute_mongodb_query: {e}")

            # Fallback: return all document IDs
            try:
                print(f"   🔄 Falling back to all documents...")
                collection = self.mongo_client.db[self.collection_name]
                cursor = collection.find({}, {"_id": 1}).limit(limit)
                mongodb_ids = [str(doc["_id"]) for doc in cursor]
                print(f"   📊 Fallback Results: {len(mongodb_ids)} IDs found")
                return mongodb_ids
            except Exception as fallback_error:
                print(f"   ❌ Fallback also failed: {fallback_error}")
                self.logger.error(f"Fallback search also failed: {fallback_error}")
                return []


    async def parse_query_components(self, user_query: str) -> Dict[str, Any]:
        """
        Parse user query and classify each component into MongoDB or ChromaDB categories.

        Args:
            user_query: Natural language query from user

        Returns:
            Dictionary with parsed components for MongoDB and ChromaDB
        """
        try:
            system_prompt = """
            You are a query parser for resume search. Your task is to analyze a natural language query
            and classify each component into either MongoDB fields or ChromaDB embedding types.

            MongoDB Fields (non-embedding, structural data):
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages
            - Resume.Achievements.Date
            - Resume.Projects.Duration, TeamSize

            ChromaDB Embedding Types (content-based, semantic search):
            1. FullName - Person's name
            2. Institution - Educational institutions
            3. CompanyName - Company/organization names
            4. Role - Job titles and positions
            5. Description/Responsibility - Job responsibilities and descriptions
            6. Skills - Technical and soft skills
            7. CertificationName - Names of certifications
            8. IssuingOrganization - Organizations that issued certifications
            9. AchievementName - Names of achievements and awards
            10. ProjectName - Names of projects
            11. Description - Project descriptions
            12. TechnologiesUsed - Technologies and tools used in projects
            13. ProjectRole - Role in projects

            Always classify City, State, Address, Degree and any location-related terms strictly under MongoDB fields. Do not include them in ChromaDB embedding types.

            Return a JSON object with this structure:
            {
                "mongodb_components": {
                    "Gender": "male",
                    "City": "Mumbai"
                },
                "chromadb_components": {
                    "Role": ["PGT Physical", "Teacher"],
                    "Skills": ["MS Word", "MS Excel", "Python"],
                    "CompanyName": ["Google", "Microsoft"]
                }
            }

            Examples:
            - "Show male who worked as PGT Physical and have skill of MS Word, MS Excel"
              → mongodb: {"Gender": "male"}, chromadb: {"Role": ["PGT Physical"], "Skills": ["MS Word", "MS Excel"]}

            - "Find female software engineers from Mumbai with Python experience"
              → mongodb: {"Gender": "female", "City": "Mumbai"}, chromadb: {"Role": ["software engineer"], "Skills": ["Python"]}

            - "show candidates who work as pgt teacher"
              → mongodb: {}, chromadb: {"Role": ["pgt"]}

            Return ONLY valid JSON, no explanations.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Parse this query: {user_query}"}
                ],
                temperature=0.7
            )

            parsed_text = response.choices[0].message.content.strip()
            print(f"🔍 DEBUG: Generated query components: {parsed_text}")

            # Parse JSON
            try:
                import json
                parsed_components = json.loads(parsed_text)

                self.logger.info(f"📋 Query parsed successfully:")
                self.logger.info(f"   MongoDB components: {parsed_components.get('mongodb_components', {})}")
                self.logger.info(f"   ChromaDB components: {parsed_components.get('chromadb_components', {})}")

                return parsed_components

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse query components: {parsed_text}")
                return {"mongodb_components": {}, "chromadb_components": {}}

        except Exception as e:
            self.logger.error(f"Error parsing query components: {e}")
            return {"mongodb_components": {}, "chromadb_components": {}}

    async def determine_embedding_types_to_search(self, user_query: str) -> List[str]:
        """
        Use OpenAI to determine which embedding types to search based on the query.

        Args:
            user_query: Original natural language query from user

        Returns:
            List of embedding types to search
        """
        try:
            system_prompt = """
            You are an embedding type selector for resume search. Your task is to analyze a natural language query
            and determine which specific embedding types should be searched for the best results.

            Available embedding types:
            1. FullName - Person's name (search when looking for specific people)
            2. Institution - Educational institutions (search when looking for graduates from specific schools)
            3. CompanyName - Company/organization names (search when looking for people from specific companies)
            4. Role - Job titles and positions (search when looking for specific job roles)
            5. Description/Responsibility - Job responsibilities (search when looking for specific work experience)
            6. Skills - Technical and soft skills (search when looking for specific skills/technologies)
            7. CertificationName - Names of certifications (search when looking for specific certifications)
            8. IssuingOrganization - Organizations that issued certifications (search when looking for certs from specific orgs)
            9. AchievementName - Names of achievements and awards (search when looking for specific achievements)
            10. ProjectName - Names of projects (search when looking for specific project names)
            11. Description - Project descriptions (search when looking for project experience)
            12. TechnologiesUsed - Technologies and tools used in projects (search when looking for specific tech experience)
            13. ProjectRole - Role in projects (search when looking for specific project roles)

            Examples:
            - "Find Dishita" → ["FullName"]
            - "Find Python developers" → ["Skills", "Role"]
            - "Find people from IIT" → ["Institution"]
            - "Find software engineers with React experience" → ["Role", "Skills", "TechnologiesUsed"]
            - "Find project managers from Google" → ["Role", "CompanyName"]
            - "Find AWS certified professionals" → ["CertificationName", "Skills"]

            Return ONLY a JSON array of embedding type names, nothing else.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Which embedding types should be searched for: {user_query}"}
                ],
                temperature=0.1
            )

            embedding_types_text = response.choices[0].message.content.strip()

            # Parse JSON array
            try:
                import json
                embedding_types = json.loads(embedding_types_text)

                # Validate embedding types
                valid_types = [
                    "FullName", "Institution", "CompanyName", "Role", "Description/Responsibility",
                    "Skills", "CertificationName", "IssuingOrganization", "AchievementName",
                    "ProjectName", "Description", "TechnologiesUsed", "ProjectRole"
                ]

                # Filter to only valid types
                filtered_types = [t for t in embedding_types if t in valid_types]

                if not filtered_types:
                    # Fallback to all types if none are valid
                    filtered_types = ["Skills", "Role", "FullName"]

                self.logger.info(f"Selected embedding types for '{user_query}': {filtered_types}")
                return filtered_types

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse embedding types: {embedding_types_text}")
                return ["Skills", "Role", "FullName"]  # Default fallback

        except Exception as e:
            self.logger.error(f"Error determining embedding types: {e}")
            return ["Skills", "Role", "FullName"]  # Default fallback


    async def verify_mongodb_filters(self, mongodb_ids: List[str], original_query: str) -> List[str]:
        """
        Verify that MongoDB IDs still match the original query filters.
        This is a post-processing step to ensure consistency.

        Args:
            mongodb_ids: List of MongoDB IDs to verify
            original_query: Original query to extract filters from

        Returns:
            Filtered list of MongoDB IDs that match the criteria
        """
        try:
            filters = self.extract_filters_from_query(original_query)
            if not filters:
                return mongodb_ids  # No filters to verify

            collection = self.mongo_client.db[self.collection_name]

            # Add _id filter to the existing filters
            from bson import ObjectId
            filters["_id"] = {"$in": [ObjectId(id_str) for id_str in mongodb_ids]}

            # Find documents that match both the ID list and the filters
            cursor = collection.find(filters, {"_id": 1})
            verified_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Verified {len(verified_ids)} out of {len(mongodb_ids)} MongoDB IDs against filters")
            return verified_ids

        except Exception as e:
            self.logger.error(f"Error verifying MongoDB filters: {e}")
            return mongodb_ids  # Return original list if verification fails
        
    async def determine_combination_logic(self, user_query: str, chromadb_components: Dict[str, List[str]]) -> str:
        """
        Generate executable Python set theory code for combining search conditions.

        Args:
            user_query: Natural language query from user
            chromadb_components: Dictionary of ChromaDB components to search

        Returns:
            Executable Python set theory expression (e.g., "condition1 & condition2 | condition3")
        """
        try:
            print(f"\n🧮 GENERATING SET THEORY LOGIC WITH OPENAI...")
            print(f"   User Query: '{user_query}'")
            print(f"   Components: {list(chromadb_components.keys())}")

            # Create condition names based on components
            condition_names = []
            for i, (emb_type, values) in enumerate(chromadb_components.items()):
                for j, value in enumerate(values):
                    condition_names.append(f"condition_{i}_{j}")  # e.g., condition_0_0, condition_0_1

            # Build condition descriptions
            condition_descriptions = []
            condition_idx = 0
            for emb_type, values in chromadb_components.items():
                for value_idx, value in enumerate(values):
                    condition_name = f"condition_{condition_idx}_{value_idx}"
                    condition_descriptions.append(f"- {condition_name}: Set of IDs matching '{value}' in '{emb_type}'")
                condition_idx += 1

            component_mapping = []
            for emb_type, values in chromadb_components.items():
                component_mapping.append(f"- {emb_type}: {values}")

            system_prompt = f"""
            You are a set theory logic generator. Your task is to analyze a natural language query and generate
            executable Python set theory code that combines search conditions using set operations.

            Available conditions (each represents a set of MongoDB IDs matching that specific condition):
            {chr(10).join(condition_descriptions)}

            Set Operations:
            - & (intersection): AND logic - IDs that match ALL conditions
            - | (union): OR logic - IDs that match ANY condition
            - - (difference): EXCLUDE logic - IDs in first set but not in second
            - ^ (symmetric difference): XOR logic - IDs in either set but not both

            Examples:
            - "Python developers who worked at Google" → "condition_0_0 & condition_1_0"
            - "Teachers or software engineers" → "condition_0_0 | condition_0_1"
            - "Python or Java developers from Google" → "(condition_0_0 | condition_0_1) & condition_1_0"
            - "Engineers who know Python but not from Microsoft" → "condition_0_0 & condition_1_0 - condition_2_0"

            Component mapping for this query:
            {chr(10).join(component_mapping)}

            Return ONLY the executable Python set theory expression using the condition names above.
            The expression should be runnable Python code that combines the condition sets.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate set theory logic for: {user_query}"}
                ],
                temperature=0.1
            )

            set_theory_code = response.choices[0].message.content.strip()
            print(f"   🔍 OpenAI Response: {set_theory_code}")

            # Validate that the code only contains allowed operations and condition names
            allowed_chars = set("condition_0123456789&|^-() ")
            if all(c in allowed_chars or c.isalnum() or c == '_' for c in set_theory_code):
                print(f"   ✅ Valid Set Theory Code: {set_theory_code}")
                return set_theory_code
            else:
                print(f"   ❌ Invalid characters in set theory code, using default")
                return " & ".join(condition_names)  # Default to AND all conditions

        except Exception as e:
            print(f"   ❌ Error generating set theory logic: {e}")
            self.logger.error(f"Error in determine_combination_logic: {e}")
            # Fallback to simple AND logic
            condition_names = []
            for i, (emb_type, values) in enumerate(chromadb_components.items()):
                for j, value in enumerate(values):
                    condition_names.append(f"condition_{i}_{j}")
            return " & ".join(condition_names) if condition_names else "set()"


    def perform_specific_component_searches(
        self,
        chromadb_components: Dict[str, List[str]],
        available_mongodb_ids: List[str],
        set_theory_logic: str = "condition_0_0",
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Perform vector-based semantic search using ChromaDB with set theory logic,
        showing a mapping of each condition to its embedding type and term.
        """
        try:
            if not chromadb_components or not available_mongodb_ids:
                return []

            # 0. Build and print condition → (embedding type, term) map
            condition_map = {}
            idx = 0
            for emb_type, terms in chromadb_components.items():
                for term_idx, term in enumerate(terms):
                    cname = f"condition_{idx}_{term_idx}"
                    condition_map[cname] = (emb_type, term)
                idx += 1

            print("\n🔍 CONDITION MAPPINGS:")
            for cname, (emb, term) in condition_map.items():
                print(f"   {cname}: embedding_type='{emb}', term='{term}'")

            # 1. Run each condition individually
            max_per_cond =  self.max_matches_per_condition
            dist_thresh = self.distance_threshold
            condition_sets: Dict[str, Set[str]] = {}
            best_distance: Dict[str, float] = {}
            per_value_distance: Dict[Tuple[str, str, str], float] = {}

            for cname, (emb_type, search_value) in condition_map.items():
                print(f"\n🎯 Searching {cname} ({emb_type}): '{search_value}'")
                embedding = self.get_embedding(search_value) or []
                if not embedding:
                    print(f"   ⚠️ No embedding generated, skipping")
                    condition_sets[cname] = set()
                    continue

                results = self.chroma_collection.query(
                    query_embeddings=[embedding],
                    n_results=len(available_mongodb_ids),
                    include=["metadatas", "distances"],
                    where={
                        "$and": [
                            {"mongodb_id": {"$in": available_mongodb_ids}},
                            {"embedding_type": emb_type}
                        ]
                    }
                )

                # filter + sort + truncate
                hits = [
                    (d, m)
                    for d, m in zip(results["distances"][0], results["metadatas"][0])
                    if d < dist_thresh
                ]
                hits.sort(key=lambda x: x[0])
                hits = hits[:max_per_cond]

                matches = set()
                for dist, meta in hits:
                    mid = meta.get("mongodb_id")
                    if not mid: continue
                    matches.add(mid)
                    best_distance[mid] = min(best_distance.get(mid, dist), dist)
                    key = (mid, emb_type, search_value)
                    per_value_distance[key] = min(per_value_distance.get(key, dist), dist)

                print(f"   ✅ {len(matches)} matches under threshold {dist_thresh}")
                condition_sets[cname] = matches

            # 2. Apply set-theory
            print(f"\n🧮 APPLYING LOGIC: {set_theory_logic}")
            try:
                matching_ids = eval(set_theory_logic, {"__builtins__": {}}, condition_sets)
            except Exception:
                matching_ids = set.intersection(*condition_sets.values()) if condition_sets else set()
            print(f"   🔍 {len(matching_ids)} IDs after combining")

            if not matching_ids:
                return []

            # 3. Assemble results (with similarity_score same as match_distance)
            final = []
            for mid in matching_ids:
                rec = self.chroma_collection.get(
                    where={"mongodb_id": mid},
                    include=["metadatas", "documents"],
                    limit=1
                )
                if not rec.get("metadatas"):
                    continue
                meta = rec["metadatas"][0]

                # show which terms hit
                preview_lines = []
                for cname, (emb, term) in condition_map.items():
                    d = per_value_distance.get((mid, emb, term))
                    preview_lines.append(f"{cname} ({emb}='{term}') → dist={d:.3f}" if d else f"{cname} ({emb}='{term}')")

                dist = best_distance.get(mid)
                final.append({
                    "mongodb_id": mid,
                    "full_name": meta.get("FullName", "N/A"),
                    "email": meta.get("Email", "N/A"),
                    "embedding_type": "Set Theory Match",
                    "match_distance": dist,
                    "similarity_score": dist,
                    "document_preview": "\n".join(preview_lines),
                    "original_filename": meta.get("original_filename", "N/A")
                })

            # 4. Sort + rank
            final.sort(key=lambda r: r["similarity_score"] or float("inf"))
            for i, r in enumerate(final[:top_k]):
                r["rank"] = i + 1

            return final[:top_k]

        except Exception as e:
            self.logger.error(f"Error in perform_specific_component_searches: {e}")
            return []




    async def search(self, nlp_query: str, top_k: int = 4, mongodb_limit: int = 1000) -> Dict[str, Any]:
        """
        Main search function that orchestrates the hybrid search process.
        
        Args:
            nlp_query: Natural language search query
            top_k: Number of top results to return
            mongodb_limit: Maximum MongoDB documents to consider
            
        Returns:
            Search results with metadata and match distances
        """
        try:
            self.logger.info(f"🚀 Starting smart hybrid search for query: '{nlp_query}'")

            # Stage 1: Parse query components using OpenAI
            self.logger.info("📋 Stage 1: Parsing query components with OpenAI...")
            parsed_components = await self.parse_query_components(nlp_query)

            mongodb_components = parsed_components.get("mongodb_components", {})
            chromadb_components = parsed_components.get("chromadb_components", {})

            # Print detailed parsing information
            print(f"\n🔍 QUERY ANALYSIS FOR: '{nlp_query}'")
            print("=" * 80)

            if mongodb_components:
                print("📊 MONGODB SEARCH (Structural Data):")
                for field, value in mongodb_components.items():
                    print(f"   {field}: {value}")
            else:
                print("📊 MONGODB SEARCH: No structural filters identified")

            if chromadb_components:
                print("\n🧠 CHROMADB SEARCH (Semantic/Content Data):")
                for emb_type, values in chromadb_components.items():
                    print(f"   {emb_type}: {values}")
            else:
                print("\n🧠 CHROMADB SEARCH: No semantic filters identified")

            # Stage 2: Generate and execute MongoDB query with OpenAI
            self.logger.info("🗄️  Stage 2: Generating and executing MongoDB query with OpenAI...")
            mongodb_ids, mongodb_query = await self.generate_and_execute_mongodb_query(nlp_query, limit=mongodb_limit)
            
            self.mongodbQuery = mongodb_query

            print(f"   📊 MongoDB Results: {len(mongodb_ids)} IDs found")
            
            if not mongodb_ids:
                print("   ❌ No MongoDB documents found matching criteria")
                return {
                    "query": nlp_query,
                    "parsed_components": parsed_components,
                    "mongodb_query": self.mongodbQuery,
                    "total_results": 0,
                    "results": [],
                    "error": "No MongoDB documents found"
                }

            # Stage 3: Perform ChromaDB component searches
            if chromadb_components:
                self.logger.info("🧠 Stage 3: Performing specific ChromaDB component searches...")

                print(f"\n🧠 CHROMADB COMPONENT SEARCH:")
                print(f"   Available MongoDB IDs: {len(mongodb_ids)}")
                print(f"   Searching for components: {list(chromadb_components.keys())}")

                # Generate set theory logic for combining conditions
                set_theory_logic = await self.determine_combination_logic(nlp_query, chromadb_components)
                search_results = self.perform_specific_component_searches(chromadb_components, mongodb_ids, set_theory_logic=set_theory_logic, top_k=top_k)


                print(f"   🎯 Component search results: {len(search_results)} matches")

            else:
                # No ChromaDB components, just return MongoDB results
                self.logger.info("📊 Stage 3: No ChromaDB components, returning MongoDB results...")

                print(f"\n📊 RETURNING MONGODB-ONLY RESULTS:")
                print(f"   Found {len(mongodb_ids)} matching documents")

                # Get basic info for MongoDB-only results
                search_results = []
                for i, mongodb_id in enumerate(mongodb_ids[:top_k]):
                    # Try to get basic info from ChromaDB
                    person_results = self.chroma_collection.get(
                        where={"mongodb_id": mongodb_id},
                        include=["metadatas"],
                        limit=1
                    )

                    if person_results.get("metadatas"):
                        metadata = person_results["metadatas"][0]
                        search_results.append({
                            "rank": i + 1,
                            "mongodb_id": mongodb_id,
                            "full_name": metadata.get("FullName", "N/A"),
                            "email": metadata.get("Email", "N/A"),
                            "embedding_type": "MongoDB Match",
                            "match_distance": 0.0,
                            "similarity_score": 1.0,
                            "document_preview": f"Matches MongoDB criteria: {list(mongodb_components.keys())}",
                            "original_filename": metadata.get("original_filename", "N/A")
                        })

            # Print final results summary
            print(f"\n🎉 FINAL RESULTS SUMMARY:")
            print(f"   Total Results: {len(search_results)}")
            if search_results:
                print(f"   Top Match: {search_results[0]['full_name']}")
                print(f"   Match Type: {search_results[0]['embedding_type']}")

            # Return comprehensive results
            return {
                "query": nlp_query,
                "parsed_components": parsed_components,
                "mongodb_query": self.mongodbQuery,
                "mongodb_components": mongodb_components,
                "chromadb_components": chromadb_components,
                "mongodb_ids_found": len(mongodb_ids),
                "total_results": len(search_results),
                "results": search_results,
                "search_stages": {
                    "stage_1": "Query components parsed with OpenAI",
                    "stage_2": f"MongoDB search: {len(mongodb_ids)} IDs found (Generated by OpenAI)",
                    "stage_3": f"ChromaDB component search: {len(search_results)} results"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {e}")
            # Always return MongoDB and ChromaDB query debug info
            return {
                "query": nlp_query,
                "mongodb_query": getattr(self, 'mongodbQuery', None),
                "chromadb_components": locals().get('chromadb_components', None),
                "total_results": 0,
                "results": [],
                "error": str(e)
            }

# Utility functions for easy usage
async def search_resumes(query: str, top_k: int = 50) -> Dict[str, Any]:
    """
    Convenience function to search resumes using the hybrid approach.
    
    Args:
        query: Natural language search query
        top_k: Number of top results to return
        
    Returns:
        Search results dictionary
    """
    search_engine = ChromaMongoSearchEngine()
    return await search_engine.search(query, top_k)

# Example usage
if __name__ == "__main__":
    async def main():
        # Example searches
        queries = [
            # "Find me a female who worked at shri g international and l g academy",
            # "Find me a femle teacher whose name is chandani and have experience of 1+ years",
            # "Find me a female whose name is chandani",
            # "Find me a female who worked at Shri g internation OR Ashashkiya Sanyogitaganj Higher",
            # "A male who worked at south valley and khalsa college",
            # "Find a female who worked as physcial instructor intern or worked at Medicaps School",
            # "A male teacher who worked at district hospital and kiddy sports private",
            "Find me a female who worked at jain shwetamber academy"
            # "Find a male whose name is yadav"
            # "Female from indore who has 'unit and lesson planning' skill or studied in Shri Cloth Market Kanya Vidhyalaya"
        ]
        
        search_engine = ChromaMongoSearchEngine()
        
        for query in queries:
            print(f"\n{'='*60}")
            print(f"Searching: {query}")
            print('='*60)
            
            results = await search_engine.search(query, top_k=50)
            
            print(f"Original Query: {results.get('query')}")
            print(f"Optimized Query: {results.get('optimized_query')}")
            print(f"MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total Results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop Results:")
                for result in results['results'][:50]:  # Show top 3
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    print(f"     Preview:\n{result['document_preview']}\n")
            
            if results.get('error'):
                print(f"Error: {results['error']}")
    
    asyncio.run(main())
