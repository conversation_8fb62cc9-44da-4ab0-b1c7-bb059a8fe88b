{"id": "f98f18c8-ea36-4e5f-b309-7f6c133c54ab", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"<PERSON><PERSON> Yadav\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Male\",\n      \"BirthDate\": \"1996-01-07\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"Shramik Colony Rau\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"9630386274\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"Intend to build a career with leading institute of hi-tech environment with committed & dedicated people, which will help me to explore myself fully and realize my potential.\",\n    \"Education\": [\n      {\n        \"Degree\": \"<PERSON>om (Bachelor of Commerce)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Khalsa College\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2016-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 53.0\n      },\n      {\n        \"Degree\": \"B.Ped\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Eklavya Institute\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Vaisnav Higher Secondary School\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2013-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 57.0\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Kala Vidhya Mandir\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2011-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 64.0\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"South Valley International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Sports Teacher (Cricket)\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Discipline Incharge\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Vedanta Global School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Sports Teacher (Cricket)\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Discipline Incharge, Sports other event Coordinator, etc\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"M.B. Khalsa College\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Sports Teacher (Cricket)\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Star Cricket Club\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Cricket Coach\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 10,\n    \"Skills\": [\n      \"Windows Operating Systems\",\n      \"MS-Office\",\n      \"Zoom\",\n      \"Google Meet\",\n      \"Google Classroom\",\n      \"Google Forms\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [\n      {\n        \"CertificationName\": \"Qualified as Scorer and Umpire\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"M.P. Cricket Association\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-01-01\",\n        \"ExpiryDate\": null\n      }\n    ],\n    \"Achievements\": [],\n    \"Languages\": [\n      \"English\",\n      \"Hindi\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is in a CSV-like structure with Page No., Text, X1, Y1, X2, Y2. There are also tables specified: Table-1, Table-2, and Table-3. I need to analyze this and reconstruct the layout using the bounding box coordinates to ensure I fetch the information correctly.\n\nStep 1: Analyze the UserContent.\n\nThe text is spread across pages, with coordinates that might help in understanding the layout, like which texts are close to each other or in sections. However, the tables are provided separately, which might simplify things.\n\nFrom the text:\n\n- Page 1 has personal info, objective, and some sections.\n\n- Page 2 has computer skills, personal profile, etc.\n\nTables:\n\n- Table-1: Education details\n\n- Table-2: Work Experience\n\n- Table-3: Additional personal info\n\nI need to reconstruct the layout. Since the tables are given in a structured format, I can use them directly for education and work experience. For other parts, I need to map the text to the appropriate sections.\n\nStep 2: Reconstruct the layout.\n\nLooking at the coordinates:\n\n- On Page 1, \"DILIP YADAV\" is at the top, likely the name.\n\n- Then contact no, email, etc.\n\n- \"CARRIER OBJECTIVE:\" and the text below.\n\n- \"ACADEMIC QUALIFICATIONS:\" which probably refers to Table-1.\n\n- On Page 2, \"Computer Proficiency/IT Skill\", \"PERSONAL PROFILE\", etc.\n\n- Table-3 has father's name, DO<PERSON>, etc.\n\nI need to group the text based on proximity. But since tables are provided, I can extract from there.\n\nStep 3: Fill out the required output based on the schema.\n\nThe output must be a JSON in the specified format. I need to extract all fields.\n\nStart with PersonalInformation:\n\n- FullName: From text, \"DILIP YADAV\" – so \"Dilip Yadav\"\n\n- FullNameEmbedding: Leave as null\n\n- Gender: Not explicitly stated. Infer from name. \"Dilip\" is a common male name in India, so infer \"Male\"\n\n- BirthDate: From Table-3, \"DATE OF BIRTH : 07/Jan/1996\" – so \"1996-01-07\" (need to format as date)\n\n- Email: From text, \"Email: -<EMAIL>\" – \"<EMAIL>\"\n\n- Address: From Table-3, \"PERMANANT ADD. : Shramik Colony Rau.\" – \"Shramik Colony Rau\"\n\n- City: \"Rau\" might be a city or part of address. From context, probably Indore area. In Table-1, institutes are in Indore, so infer City as \"Indore\". But schema has City with specific enums or pattern. \"Indore\" is in the list under cities.\n\n- State: Madhya Pradesh, as per boards in tables. \"Madhya Pradesh\" is in the state enum.\n\n- ContactNumber: Multiple mentions, but \"9630386274\" is consistent.\n\n- LinkedInProfile: Not mentioned, so leave as empty or null? Schema says string, but if not found, should be ''. According to step 3, if not found, keep as ''.\n\nSchema for strings is type \"string\", so I should use an empty string if not found.\n\nNow, Objective: From text, \"CARRIER OBJECTIVE: Intend to build a career with leading institute of hi-tech environment with committed & dedicated people, which will help me to explore myself fully and realize my potential.\" So, combine that.\n\nEducation: From Table-1. Need to order from highest to lowest.\n\nTable-1:\n\n- B.Ped, Eklavya Institute, Barktulla, Perusing, -\n\n  - Degree: B.Ped – not in the enum list. Enum has BEd, but not B.Ped. Probably Bachelor of Physical Education. I should use the fallback pattern, so \"B.Ped\"\n\n  - Specialization: Not specified. Perhaps infer from context, but better to leave as empty or null? Schema says type string, so if not found, ''. But in the degree, it might imply something. In work experience, he's a sports teacher, so perhaps related. But not specified, so leave as ''.\n\n  - Institution: \"Eklavya Institute\" – standardize. Schema says avoid abbreviations, so \"Eklavya Institute\". But in table it's \"Eklavya Institute\", and university is \"Barktulla\" which might be Barkatullah University. In Board/University column, it's \"Barktulla\", probably typo for Barkatullah. Schema says standardize, so perhaps \"Barkatullah University\" for university, but the field is Institution, which is \"Eklavya Institute\".\n\n  In the education object, Institution is \"The full, standardized name of the educational institution.\" So for B.Ped, Institution is \"Eklavya Institute\", but the university is mentioned separately. I think Institution should be \"Eklavya Institute\".\n\n  - GraduationYear: \"Perusing\" – probably \"Pursuing\", so not graduated. Schema is GraduationYear, format date, but it's ongoing. Perhaps leave as null or empty. Table says \"Perusing\", so maybe set GraduationYear to null.\n\n  - GPA, MaxGPA, Percentage: \"-\" , so not mentioned. Percentage is blank.\n\n- Indore Division Cricket Association, -, M.P. Cricket Association, 2022, Qualified Scorer and Umpire\n\n  - This doesn't look like a standard degree. Perhaps it's a certification or something else. But it's in the education table. Degree field is blank or inferred? In Table-1, the first column is \"Degree\", here it's \"Indore Division Cricket Association\", which might be the institute, but it's messy.\n\n  Looking back: \n\n  Degree, Institute/School, Board/University, Year of Passing, Percentage\n\n  B.Ped, Eklavya Institute, Barktulla, Perusing, -\n\n  Indore Division Cricket Association, -, M.P. Cricket Association, 2022, Qualified Scorer and Umpire\n\n  B.Com, Khalsa College, D.A.V.V., 2016, 53%\n\n  etc.\n\n  So, for the second row, Degree is not specified, Institute is \"Indore Division Cricket Association\", etc. Perhaps it's not a degree, but a course or certification. But it's in the academic qualifications table. I need to fit it into Education array.\n\n  Perhaps treat \"Indore Division Cricket Association\" as the institution, and \"Qualified Scorer and Umpire\" as the degree or something. But schema has specific degrees. Maybe use fallback.\n\n  Better to list all as education entries.\n\n  Order: Highest to lowest. Degrees are B.Ped (pursuing), then B.Com, Higher Secondary, High School.\n\n  B.Ped might be bachelor level, B.Com is bachelor, then 12th, 10th.\n\n  The cricket thing might not be a formal education, but it's listed.\n\n  Also, \"Qualified Scorer and Umpire\" could be seen as a certification.\n\n  But schema has separate Certifications array.\n\n  I need to decide where to put it. Step 4 says assign to most appropriate category.\n\n  Probably, formal degrees in Education, and this might fit in Certifications or Achievements.\n\n  Looking at the text, it's under ACADEMIC QUALIFICATIONS, so perhaps keep in Education.\n\n  For now, I'll put it in Education.\n\n  So, list:\n\n  - B.Ped, pursuing\n\n  - Indore Division Cricket Association qualification\n\n  - B.Com\n\n  - Higher Secondary\n\n  - High School\n\n  Order by level: B.Ped and B.Com are both bachelor, but B.Ped is pursuing, so perhaps list completed first or as is. Schema says Highest to Lowest order, like PhD > Masters > Bachelors > etc.\n\n  So, Bachelors first: B.Com completed, B.Ped pursuing. Typically, pursuing is listed last or separately, but schema doesn't specify. I'll list in chronological or level order. Perhaps sort by GraduationYear or inferred level.\n\n  Degrees: B.Com (2016), Higher Secondary (2013), High School (2011), B.Ped pursuing, and the cricket one in 2022.\n\n  But cricket one might not be a degree. Perhaps exclude from Education and put in Certifications.\n\n  In Table-1, it's under Degree, but the first column has \"Indore Division Cricket Association\" which might be misaligned. No, it's listed as Degree for that row.\n\n  To avoid duplication, perhaps treat it as a degree with Degree=\"Qualified Scorer and Umpire\", Institution=\"Indore Division Cricket Association\", etc.\n\n  But better to use the categories properly.\n\n  Moving on.\n\n  For Education array, required fields: Degree, Specialization, Institution, etc.\n\n  For B.Ped: Degree=\"B.Ped\", Specialization='', Institution=\"Eklavya Institute\", GraduationYear=null (since pursuing), GPA=null, MaxGPA=null, Percentage=null\n\n  For the cricket one: Degree=\"Qualified Scorer and Umpire\", but not in enum, so use pattern. Specialization='', Institution=\"Indore Division Cricket Association\", Board/University=\"M.P. Cricket Association\", Year=\"2022\", Percentage not numerical.\n\n  Perhaps Percentage can be left as null, or if \"Qualified\" is not a percentage, set to null.\n\n  Similarly for others.\n\n  B.Com: Degree=\"B.Com\", Specialization='', Institution=\"Khalsa College\", GraduationYear=\"2016\", Percentage=53, so GPA and MaxGPA might need inference. Percentage is given, so use that. Schema says if percentage mentioned, extract it. GPA can be calculated if needed, but percentage is directly given.\n\n  Schema has GPA and Percentage. If percentage is given, set Percentage, GPA might be null unless mentioned.\n\n  In this case, percentage is given for some.\n\n  Also, MaxGPA: need to infer. For example, for board exams, max might be 100, for universities vary. But schema says use knowledge to infer.\n\n  For M.P. Board, high school and higher secondary, max GPA is probably 100 for percentage scale.\n\n  For B.Com, D.A.V.V. (Devi Ahilya Vishwavidyalaya), probably out of 100 or GPA scale, but percentage given, so perhaps assume max 100 for percentage calculation, but since percentage is directly given, set Percentage, and GPA null.\n\n  Schema says: \"Percentage\": \"Convert GPA to percentage using: (GPA / MaxGPA) * 100. If percentage is explicitly mentioned, extract it.\"\n\n  Here percentage is mentioned, so extract it directly.\n\n  GPA and MaxGPA can be null if not mentioned.\n\nNow, WorkExperience: From Table-2.\n\nTable-2:\n\nS.no., Institute, Post, Duration, Other Responsibilities\n\n1, South Valley International School, Sports Teacher(Cricket), 1 years, Discipline Incharge\n\n2, Vedanta Global School, Sports Teacher(Cricket), 2 years, \"Discipline Incharge, Sports other event Coordinator ,etc\"\n\n3, M.B. Khalsa College, Sports Teacher(Cricket), 2 years, 0\n\n4, Star Cricket Club, Cricket Coach, 6 years, 0\n\nNote: In the text, there's \"Experience, 10 years of experience in the field of academics\" on page 1.\n\nAlso, TotalWorkExperienceInYears: mentioned as 10 years, so use that.\n\nFor WorkExperience array, order: Highest to lowest role and latest first. Roles are all similar, Sports Teacher or Coach. Duration suggests Star Cricket Club is longest, but latest might be the schools.\n\nDates not given, only duration. In the table, Duration is in years, but not start and end years.\n\nSchema requires StartYear and EndYear.\n\nIn the table, only duration, e.g., 1 year, 2 years.\n\nAlso, in the description, \"10 years total\".\n\nI need to infer start and end years.\n\nFrom education, born in 1996, B.Com in 2016, so probably started working after that.\n\nTotal 10 years, so perhaps from 2013 or so, but Higher Secondary in 2013, so after 2016.\n\nB.Com in 2016, then work experience.\n\nTable has Star Cricket Club 6 years, others shorter.\n\nPerhaps latest is the schools.\n\nBut no specific years.\n\nI might need to set StartYear and EndYear as null or approximate.\n\nSchema allows null for dates.\n\nDuration is given, but not specific years.\n\nFor each entry, I can set StartYear and EndYear as null, and use the description.\n\nBut schema requires them, type [\"string\",\"null\"], so can be null.\n\nAlso, Role: \"Sports Teacher(Cricket)\" – should include specialization, so \"Sports Teacher (Cricket)\"\n\nSimilarly for others.\n\nOrder: Latest first. But without years, perhaps order by the table or by duration. Table is listed with S.no. 1 to 4, probably chronological or reverse. S.no. 1 is South Valley, 1 year, might be recent.\n\nIn the duration, total 1+2+2+6=11 years, close to 10.\n\nPerhaps assume the order in table is chronological, with S.no.1 being most recent or least. Typically, resumes list latest first, but here S.no.1 might be first job.\n\nTo be safe, I'll list as per table, but schema says \"latest work experience first\". Since no dates, I'll assume the order given or infer.\n\nFor now, I'll extract as is and order later.\n\nTotalWorkExperienceInYears: 10, as mentioned.\n\nSkills: From page 2, \"Computer Proficiency/IT Skill\", lists Operating Systems, Packages, Online Teaching Platform.\n\nSo, skills could be: Windows OS, MS-Office, Zoom, Google Meet, etc.\n\nList as array of strings.\n\nCertifications: Not explicitly mentioned, but the cricket qualification might fit here. Also, in education table, the scorer and umpire.\n\nFrom Table-1, \"Indore Division Cricket Association\" entry might be a certification.\n\nAchievements: None mentioned explicitly.\n\nLanguages: From Table-3, \"LANGUAGE KNOWN : English, Hindi\"\n\nProjects: Not mentioned.\n\nNow, Objective: From text.\n\nEducation array: Need to standardize.\n\nFirst, list all education entries from Table-1.\n\n- Degree: B.Ped, Institution: Eklavya Institute, Board/University: Barktulla (probably Barkatullah University), Year: Pursuing, Percentage: -\n\n  So, Degree: \"B.Ped\", Specialization: '', Institution: \"Eklavya Institute\", GraduationYear: null, GPA: null, MaxGPA: null, Percentage: null\n\n- Degree: Indore Division Cricket Association (wait, no, in table, first column is \"Indore Division Cricket Association\", but it's likely the institute for a qualification. Perhaps Degree is blank, or \"Cricket Scorer and Umpire Qualification\" or something. Better to interpret as Degree: \"Qualified as Scorer and Umpire\", but not standard. Perhaps move to Certifications.\n\nTo avoid double-counting, I'll put formal degrees in Education and this in Certifications.\n\nSimilarly, B.Com, Higher Secondary, High School are standard.\n\nSo, for Education:\n\n- B.Ped: pursuing\n\n- B.Com\n\n- Higher Secondary (12th Pass)\n\n- High School (10th Pass)\n\nOrder: Highest to lowest: B.Ped (bachelor, pursuing), B.Com (bachelor), 12th Pass, 10th Pass. Since B.Ped is pursuing, perhaps list completed degrees first.\n\nSchema says \"Highest to Lowest order, like PhD, MBA, MSc, BSc, 10th Pass, 12th Pass.\" It says 10th Pass, 12th Pass, but typically 12th is higher than 10th.\n\nProbably typo, should be 12th > 10th.\n\nIn the note: \"PhD > Masters > Bachelors > Diploma > 12th Pass > 10th Pass\"\n\nYes, so 12th > 10th.\n\nFor B.Ped and B.Com both bachelors, list by completion date or as is.\n\nI'll list B.Com first (completed), then B.Ped (pursuing), then 12th, then 10th.\n\nBut pursuing might be listed last. For now:\n\nEntries:\n\n1. Degree: \"B.Com (Bachelor of Commerce)\" – match enum if possible, \"BCom\" is in enum.\n\nEnum has \"BCom (Bachelor of Commerce)\", so use that.\n\nSpecialization: not mentioned, so ''\n\nInstitution: \"Khalsa College\" – standardize. In table, \"Khalsa College\", and in schema, avoid abbreviations, so \"Khalsa College\". But might be part of university, but field is institution name.\n\nGraduationYear: \"2016-01-01\" or just \"2016\"? Schema format \"date\", so perhaps \"2016-01-01\" if only year, or keep as string \"2016\".\n\nFormat is \"date\", so probably ISO format, but if only year, use \"2016-01-01\" or something. But better to use the year as is, perhaps \"2016-12-31\" for end of year, but I think string \"2016\" might work, but schema says format:\"date\", so use \"2016-01-01\".\n\nSimilarly for others.\n\nPercentage: 53, so set Percentage=53.0, GPA=null, MaxGPA=null since percentage given.\n\n2. Degree: \"B.Ped\" – not in enum, so use \"B.Ped\". Or infer \"B.P.Ed (Bachelor of Physical Education)\" but not in list, so use pattern \"B.Ped\"\n\nInstitution: \"Eklavya Institute\", GraduationYear: null, Percentage: null\n\n3. Higher Secondary: Degree=\"12th Pass\", Institution=\"Vaisnav Higher Secondary School\", Board/University=\"M.P. Board\", Year=\"2013\", Percentage=57\n\n4. High School: Degree=\"10th Pass\", Institution=\"Kala Vidhya Mandir\", Board/University=\"M.P. Board\", Year=\"2011\", Percentage=64\n\nNow, the cricket one: perhaps not in Education. Put in Certifications.\n\nCertificationName: \"Qualified Scorer and Umpire\", IssuingOrganization: \"M.P. Cricket Association\", IssueDate: \"2022-01-01\", ExpiryDate: null\n\nWorkExperience from Table-2:\n\n- CompanyName: \"South Valley International School\", Role: \"Sports Teacher (Cricket)\", StartYear: null, EndYear: null, Description/Responsibility: \"Discipline Incharge\" (from Other Responsibilities)\n\nSchema requires Description/Responsibility, so use the post and other res.\n\nSimilarly for others.\n\nOrder: Latest first. Assuming the table is listed with latest first or chronological. S.no.1 might be most recent. Duration 1 year, 2 years, etc. Total 11 years, started perhaps early.\n\nI'll list as per table order, but try to order by assumed recency. Perhaps S.no.1 is recent.\n\nFor StartYear and EndYear, since not specified, set to null.\n\nTotalWorkExperienceInYears: 10\n\nSkills: From computer proficiency: Operating Systems: Windows, Packages: MS-Office, Online Teaching: Zoom, Google Meet, etc.\n\nAlso, from strengths and hobbies, but skills are professional. So, \"Windows Operating Systems\", \"MS-Office\", \"Zoom\", \"Google Meet\", \"Google Classroom\", \"Google Forms\"\n\nList as array.\n\nCertifications: The cricket one, and perhaps others not mentioned.\n\nAchievements: Not specified.\n\nLanguages: \"English, Hindi\"\n\nProjects: Not mentioned, so empty array.\n\nNow, for embeddings, all should be null as per instruction.\n\nOutput must be JSON string.\n\nNow, compile all.\n\nFirst, PersonalInformation:\n\n- FullName: \"Dilip Yadav\"\n\n- Gender: \"Male\" (inferred)\n\n- BirthDate: \"1996-01-07\" (from \"07/Jan/1996\")\n\n- Email: \"<EMAIL>\"\n\n- Address: \"Shramik Colony Rau\"\n\n- City: \"Indore\" (inferred from context, Rau is suburb of Indore)\n\n- State: \"Madhya Pradesh\" (from boards)\n\n- ContactNumber: \"9630386274\"\n\n- LinkedInProfile: \"\" (not mentioned)\n\nObjective: \"Intend to build a career with leading institute of hi-tech environment with committed & dedicated people, which will help me to explore myself fully and realize my potential.\"\n\nEducation: Array, ordered highest to lowest.\n\n- First: B.Com, since completed bachelor\n\nDegree: \"BCom (Bachelor of Commerce)\"\n\nSpecialization: \"\"\n\nInstitution: \"Khalsa College\" – but in table, it's \"M.B. Khalsa College\" in work experience, wait no, in education it's \"Khalsa College\". In work experience Table-2, there's \"M.B. Khalsa College\" for work. So for education, \"Khalsa College\" might be the same or different. Probably different entities. Keep as \"Khalsa College\"\n\nInstitutionEmbedding: null\n\nGraduationYear: \"2016-01-01\" (approximate)\n\nGPA: null\n\nMaxGPA: null\n\nPercentage: 53.0 (as number)\n\nIn schema, Percentage is number, so 53.0\n\nSimilarly,\n\n- B.Ped: Degree: \"B.Ped\", Specialization: \"\", Institution: \"Eklavya Institute\", GraduationYear: null, GPA: null, MaxGPA: null, Percentage: null\n\n- 12th Pass: Degree: \"12th Pass\", Specialization: \"\", Institution: \"Vaisnav Higher Secondary School\", GraduationYear: \"2013-01-01\", Percentage: 57.0\n\n- 10th Pass: Degree: \"10th Pass\", Specialization: \"\", Institution: \"Kala Vidhya Mandir\", GraduationYear: \"2011-01-01\", Percentage: 64.0\n\nOrder: B.Com > B.Ped > 12th Pass > 10th Pass, but B.Ped is pursuing, so perhaps B.Com > 12th > 10th > B.Ped. But schema says highest to lowest, so probably by level, with pursuing last. I'll do B.Com, then 12th, then 10th, then B.Ped.\n\nNo, B.Ped is also bachelor level, higher than 12th. Perhaps list all bachelors together.\n\nTo simplify, list by graduation year descending for completed: B.Com 2016, 12th 2013, 10th 2011, then pursuing B.Ped.\n\nBut schema doesn't specify, so I'll list as: highest level first, so B.Com and B.Ped are both bachelor, list B.Com first as completed, then B.Ped.\n\nIn many resumes, pursuing degrees are listed with status.\n\nI'll order: B.Com, B.Ped, 12th Pass, 10th Pass.\n\nNow, for the cricket qualification, I'll put it in Certifications.\n\nCertifications:\n\n- CertificationName: \"Qualified as Scorer and Umpire\", IssuingOrganization: \"M.P. Cricket Association\", IssueDate: \"2022-01-01\", ExpiryDate: null\n\nWorkExperience: From Table-2, order latest first. Assuming S.no.1 is most recent, so:\n\n- CompanyName: \"South Valley International School\", Role: \"Sports Teacher (Cricket)\", StartYear: null, EndYear: null, Description: \"Discipline Incharge\"\n\n- Then \"Vedanta Global School\", Role: \"Sports Teacher (Cricket)\", Duration: 2 years, Description: \"Discipline Incharge, Sports other event Coordinator, etc\"\n\n- Then \"M.B. Khalsa College\", Role: \"Sports Teacher (Cricket)\", Duration: 2 years, Description: \"\" (since \"0\", probably none)\n\n- Then \"Star Cricket Club\", Role: \"Cricket Coach\", Duration: 6 years, Description: \"\"\n\nTotalWorkExperienceInYears: 10\n\nSkills: [\"Windows Operating Systems\", \"MS-Office\", \"Zoom\", \"Google Meet\", \"Google Classroom\", \"Google Forms\"] – from computer skills. Also, perhaps from other parts, but keep professional.\n\nStrengths are mentioned: Optimistic, etc., but not skills. Hobbies not skills.\n\nSo, only IT skills mentioned.\n\nLanguages: [\"English\", \"Hindi\"]\n\nAchievements: Not mentioned, so empty array.\n\nProjects: Empty.\n\nNow, for Description/Responsibility in WorkExperience, use the Other Responsibilities field.\n\nAlso, in Role, include specialization: already has \"Cricket\".\n\nNow, compile JSON.\n\nFirst, ensure all fields are covered.\n\nOutput must be a single JSON string."}}], "created": 1750943494, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 935, "prompt_tokens": 5142, "total_tokens": 11270, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 5193, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 9, "text_tokens": 5142, "image_tokens": 0}, "num_sources_used": 0}}