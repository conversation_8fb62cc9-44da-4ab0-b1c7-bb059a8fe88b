{"id": "7520791b-094f-4011-a47b-a045d77c6573", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"<PERSON><PERSON><PERSON>\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Female\",\n      \"BirthDate\": \"1979-11-21\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"303, <PERSON><PERSON><PERSON>, 11 Indralok Colony, INDORE (M.P.) - 452009\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"9826974111\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"To leverage my knowledge of best practices in curriculum development, instructional leadership, and staff supervision to ensure the success of students and faculty.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MEd (Master of Education)\",\n        \"Specialization\": \"Guidance & Counseling, Educational Administration\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2022-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 77.16\n      },\n      {\n        \"Degree\": \"MSc (Master of Science)\",\n        \"Specialization\": \"Botany\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2003-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 71.33\n      },\n      {\n        \"Degree\": \"BEd (Bachelor of Education)\",\n        \"Specialization\": \"Biology, Chemistry\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2001-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 66.66\n      },\n      {\n        \"Degree\": \"BSc (Bachelor of Science)\",\n        \"Specialization\": \"Botany, Zoology, Chemistry\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2000-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 67.87\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"Biology, Chemistry, Physics, Mathematics\",\n        \"Institution\": \"CBSE\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"1997-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 71\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"All Compulsory Subjects\",\n        \"Institution\": \"CBSE\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"1995-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 76.8\n      },\n      {\n        \"Degree\": \"UGC NET (University Grants Commission National Eligibility Test)\",\n        \"Specialization\": \"Education\",\n        \"Institution\": \"Department of Higher Education (Government of India)\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2022-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 97.28\n      },\n      {\n        \"Degree\": \"CTET (Central Teacher Eligibility Test)\",\n        \"Specialization\": \"Science, Mathematics\",\n        \"Institution\": \"CBSE\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2018-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"AWES (Online Screening Test)\",\n        \"Specialization\": \"PGT Biology\",\n        \"Institution\": \"Army Welfare Education Society\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2022-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"\",\n        \"Specialization\": \"All compulsory subjects\",\n        \"Institution\": \"Madurai Kamaraj University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2008-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 56\n      },\n      {\n        \"Degree\": \"\",\n        \"Specialization\": \"All compulsory subjects\",\n        \"Institution\": \"Indira Gandhi National Open University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2009-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 61\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Ideal Academy\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Vice Principal\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2021-01-01\",\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Academic planning, auditing classes, counseling educators, planning SOPs, conducting lesson plan counseling, research and presentations, webinars, PTM management, social media content, conflict resolution, examination handling, student counseling, coordination, directing assemblies, classroom teaching, staff evaluations, record keeping, policy discussions, and assisting principal.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"The Labour India Public School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"PGT Biology Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2020-09-14\",\n        \"EndYear\": \"2021-10-31\",\n        \"Description/Responsibility\": \"Teaching Biology to classes XI and XII online using Zoom and Google Meet, conducting weekly tests via Google Forms, and monthly exams through Google Documents.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"The Shishukunj International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"TGT Science Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2013-11-07\",\n        \"EndYear\": \"2020-10-31\",\n        \"Description/Responsibility\": \"Teaching Science and Biology to classes VI to X using interactive whiteboards, house mistress duties for co-curricular activities, core team member for British Council projects on Organic Farming and Waste Management.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"National Public School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"TGT Science Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2011-04-01\",\n        \"EndYear\": \"2013-10-31\",\n        \"Description/Responsibility\": \"Teaching EVS, Science, and Biology to classes V to X, class teacher duties, house coordination for inter-house competitions, conduction and evaluation of co-curricular activities, head of Science club organizing quizzes and exhibitions, and representing in interschool events.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Progressive Education\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"PRT Science and Mathematics Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2010-07-01\",\n        \"EndYear\": \"2011-03-31\",\n        \"Description/Responsibility\": \"Teaching Environmental Education, Science, Mathematics, Chemistry, and Biology to classes III to IX, class teacher duties, lesson planning, preparation of worksheets, and resource utilization.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Divine Child International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"PRT Science Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2005-06-08\",\n        \"EndYear\": \"2010-04-30\",\n        \"Description/Responsibility\": \"Teaching all subjects to classes II to IV, class teacher duties, planning and preparation of course books, worksheets, lesson plans, teaching aids, training new teachers, and assisting in co-curricular activities.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 19,\n    \"Skills\": [\n      \"Resilience and Courage\",\n      \"Integrity\",\n      \"Effective Communication\",\n      \"Collaboration\",\n      \"Delivering Results\",\n      \"Accountability\",\n      \"Management Skills\",\n      \"Inspiring Direction and Purpose\",\n      \"Managing Reform and Change\",\n      \"Self-Management\",\n      \"Comprehensive Content Knowledge\",\n      \"Lesson Planning\",\n      \"Adaptability in Teaching\",\n      \"Knowledge of NEP 2020\",\n      \"Bloom's Taxonomy\",\n      \"Classroom Management\",\n      \"MS-Office\",\n      \"Internet Usage\",\n      \"Zoom and Google Meet for Online Teaching\",\n      \"G-Suite\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [\n      {\n        \"CertificationName\": \"UGC-NET Qualified\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Department of Higher Education (Government of India)\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"CTET (Central Teacher Eligibility Test)\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"CBSE\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2018-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"AWES (Online Screening Test)\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Army Welfare Education Society\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Post B.Ed. Diploma in School Administration\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madurai Kamaraj University\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Post Graduate Diploma in School Leadership & Management\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Indira Gandhi National Open University\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2009-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Interpersonal Skills\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-09\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"XSEED Education\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-11\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Stress Management\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-11\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Excellence in Education\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Dheeraj Mehrotra\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-11\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Educomp Interactive White Board\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Educomp\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2011-06-08\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Braintech Workshop\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2013-07-02\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Chemistry Popularization\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Pradeep Srivastava\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2013-07-06\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Secret of Life\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"R.J. Abhijeet\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2014-01-04\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"ASSET Workshop\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2015-03-17\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Lodestar Career Guidance\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Ms. Ramya\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Effective Communication\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Madhu Sharma\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2019-06-07\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Effective Classroom Management\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. S. Dijju\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2019-06-11\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Successful Strategies for Effective Classroom Management Using Mind Maps\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Dheeraj Mehrotra\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2019-06-12\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Pedagogy in the Age of Internet\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Sapna Agrawal\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Reimagining Education and Role of Educators as Leaders\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Himanshu Roy\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"To be Futuristic\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Mr. Shailendra Gupta\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2017-06-24\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Mental Health\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Thomas, NIMHNS Bangalore\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2018-04-13\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Happiness is a Habit, Cultivate it\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Dr. Rawat, Bombay Hospital, Indore\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2018-09-11\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Perspective Resource Person Training on Art Integration\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"CBSE, Ms. Anu Bhatia and Ms. Anjali Jagdev\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-12-04\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Early Childhood Education\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Sonal Ravi Andrew\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2023-11-04\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"G-Suite Training\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-06-03\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Competency Based Education Module 1\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Diksha\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-03-23\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"School Leadership: Concepts and Applications\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Diksha\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-03-23\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Biology XI Part 1 and Part 2\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Swayam Portal\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-02-10\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Biology XII Part 1\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Swayam Portal\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-02-10\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Enhancing Practical Skills in Biology\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"CBSE, Poonam Khurana\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-08-20\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Teacher Energized Resource Material\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-08-22\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Assessment for Secondary Classes - Bloom's Taxonomy and Questioning Techniques\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"CBSE\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-08-27\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Keep Learning by Byju's: How Teachers can adapt from Blackboard to Digital Screen\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"The Times of India\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"CENTA TPO\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Centre for Teacher Accreditation\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2016-01-01\",\n        \"ExpiryDate\": null\n      }\n    ],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"Gold Medalist in M.Sc.\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Devi Ahilya Vishwavidyalaya\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2003-01-01\"\n      },\n      {\n        \"AchievementName\": \"Certificate of Participation in Outdoor Experiential Education Programme\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Divine Child School\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2006-11-26\"\n      },\n      {\n        \"AchievementName\": \"Certificate of Participation in 13th National Athletic Meet\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Divine Child School\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2008-01-01\"\n      },\n      {\n        \"AchievementName\": \"Certificate of Participation in Pariksha Pe Charcha\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"Certificate of Participation in Online Quiz Competition\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Youth Hostels Association of India\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"Heritage Walk with School Children\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"CBSE Evaluator for Biology Class XII\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"CBSE\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"Sponsored Educational Visit to NASA\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"National Aeronautics and Space Administration\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      }\n    ],\n    \"Languages\": [\n      \"Hindi\",\n      \"English\",\n      \"Gujarati\",\n      \"Kannada\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is a structured CSV-like text with page numbers, text, and bounding box coordinates. I need to analyze this to reconstruct the layout and extract information for the resume.\n\nThe output must be a single JSON string following the <user_json_schema> provided. I have to fill in all the fields, and if something isn't found, leave it as null or empty string as specified.\n\nKey points from the instructions:\n\n- Step 1: Analyze the UserContent with coordinates to understand the layout.\n\n- Step 2: Reconstruct the layout using bounding box coordinates to properly fetch information.\n\n- Step 3: Fill out the required output in the JSON format.\n\n- Step 4: Assign data to appropriate categories without duplication, handle typos or inconsistencies.\n\n- Step 5: If not specified, set to null.\n\nEmbeddings like FullNameEmbedding, InstitutionEmbedding, etc., should be left as null since I'm extracting from the resume.\n\nNow, let's reconstruct the layout. The coordinates (X1, Y1, X2, Y2) likely represent the bounding box of the text on the page. I need to consider the relative positions to group related information, like sections in the resume.\n\nFrom the content:\n\n- Page 1 starts with personal info, objective, and work experience.\n\n- Page 2 continues work experience.\n\n- Page 3 has educational qualifications in tables.\n\n- Page 4 has skills, workshops, etc.\n\n- Page 5 has seminars, guidance, special trainings.\n\n- Page 6 has more on NEP 2020.\n\n- Page 7 has miscellaneous, personal details, etc.\n\nThere are also Table-1 to Table-5 provided separately, which contain structured data for education, qualifications, etc.\n\nI should use both the textual content and the tables to extract information.\n\nLet me outline the sections based on the text and tables:\n\n1. **Personal Information:**\n\n   - From Page 1: \"Bhavana Gupta, M.Sc., M.Ed., UGC-NET Qualified (Edu.)\" – This seems like the full name.\n\n   - Address: \"303, Mohini Mahal Apartment, 11 Indralok Colony, INDORE (M.P.) - 452009\"\n\n   - Email: \"<EMAIL>\"\n\n   - Contact Number: \"Mo. No.s- 9826974111, 9926106454\" – I should pick one or combine? Probably use the first or both if needed, but field is \"ContactNumber\", so perhaps \"9826974111\" or store as string.\n\n   - Gender: Not explicitly stated. Need to infer from name. \"Bhavana Gupta\" sounds female, so infer \"Female\".\n\n   - BirthDate: Not directly given. In Table-5, there's \"Date of Birth,21 November 1979\", so use that.\n\n   - City: INDORE\n\n   - State: M.P. (Madhya Pradesh)\n\n   - LinkedInProfile: Not mentioned, so null.\n\n2. **Objective:**\n\n   - From Page 1: \"Professional Objective: To leverage my knowledge of best practices in curriculum development, instructional leadership, and staff supervision to ensure the success of students and faculty.\"\n\n   - Combine the two lines for a full objective.\n\n3. **Education:**\n\n   - There are tables: Table-1, Table-2, Table-3 for educational qualifications.\n\n   - Need to list in highest to lowest order: PhD > Masters > Bachelors > etc.\n\n   - From Table-1:\n\n     - M.Sc.: Degree, Specialization: Botany, Institution: DAVV, Indore, Year: 2003, %: 71.33%, GPA and MaxGPA not given, but percentage is.\n\n     - B.Sc.: Degree, Specialization: Botany, Zoology, Chemistry, Institution: DAVV, Indore, Year: 2000, %: 67.87%\n\n     - 12th: AISSCE, CBSE, Year: 1997, %: 71.00%\n\n     - 10th: AISSE, CBSE, Year: 1995, %: 76.80%\n\n   - From Table-2:\n\n     - M.Ed.: Degree, Specialization: Guidance & Counseling. Educational Administration, Institution: DAVV, Indore, Year: 2022, %: 77.16%\n\n     - B.Ed.: Degree, Specialization: Biology, Chemistry, Institution: DAVV, Indore, Year: 2001, %: 66.66%\n\n     - UGC-NET Qualified: Not a degree, but a qualification. Might fit under certifications or education. Schema has \"Degree\" with enum, UGC-NET is in the list, so can use \"UGC-NET Qualified\".\n\n   - From Table-3:\n\n     - Post B.Ed. Diploma in School Administration: Degree could be \"D.El.Ed\" or similar, but not exact match, use pattern. Year: 2008, %: 56%\n\n     - Post Graduate Diploma in School Leadership & Management: Could map to \"PGDCA\" or use string.\n\n     - CTET: In the enum list.\n\n     - AWES: Not in enum, use string.\n\n   - Order: Highest to lowest. M.Ed. is masters level, M.Sc. is also masters, B.Ed. is bachelors, etc. Typically, M.Ed. and M.Sc. both masters, but list by year or importance. Schema says PhD > Masters > Bachelors > etc., so group masters together.\n\n     - M.Ed. 2022\n\n     - M.Sc. 2003\n\n     - B.Ed. 2001\n\n     - B.Sc. 2000\n\n     - 12th 1997\n\n     - 10th 1995\n\n     - Then diplomas and certifications.\n\n     - For degrees, extract Degree, Specialization, Institution, GraduationYear, GPA (null, since percentage given), MaxGPA (infer or null), Percentage (given).\n\n     - Institution: Standardize name, e.g., \"DAVV, Indore\" should be \"Devi Ahilya Vishwavidyalaya\" or keep as is? Schema says \"full, standardized name\", so perhaps \"Devi Ahilya Vishwavidyalaya, Indore\" but it's given as \"DAVV\", so use \"Devi Ahilya Vishwavidyalaya\" based on common knowledge.\n\n       - From text, it's \"DAVV, Indore\", and in schema, it says avoid abbreviations, so I should expand it. DAVV is Devi Ahilya Vishwavidyalaya.\n\n       - Similarly, for others.\n\n4. **Work Experience:**\n\n   - From pages 1 and 2, detailed work experiences are listed.\n\n   - Need to list in order: latest first or highest role. Schema says \"Highest to Lowest order, like CEO > Manager > Intern\" and \"latest work experience first and latest work experience last\" – this seems contradictory. Probably means sort by seniority or by time. Reading: \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern. (CEO > Manager > Intern) as well as with latest work experience first\"\n\n     - I think it means sort by role hierarchy with latest first. But it's a bit ambiguous. Perhaps sort chronologically with latest first, and within that, consider role.\n\n     - From text:\n\n       - Current: IDEAL ACADEMY, Vice Principal from 01/01/2021 till date.\n\n       - Then THE LABOUR INDIA PUBLIC SCHOOL, PGT Biology from 14/09/2020 to 31/10/2021.\n\n       - THE SHISHUKUNJ INTERNATIONAL SCHOOL, TGT Science from 07/11/2013 to 31/10/2020.\n\n       - NATIONAL PUBLIC SCHOOL, TGT Science from 01/04/2011 to 31/10/2013.\n\n       - PROGRESSIVE EDUCATION, PRT Science and Mathematics from 01/07/2010 to 31/03/2011.\n\n       - DIVINE CHILD INTERNATIONAL SCHOOL, PRT Science from 08/06/2005 to 30/04/2010.\n\n     - Roles: Vice Principal > PGT > TGT > PRT, so highest role is Vice Principal.\n\n     - Latest is Vice Principal.\n\n     - So list with latest first: IDEAL ACADEMY (Vice Principal), then LABOUR INDIA (PGT), etc.\n\n     - For each, extract CompanyName, Role (include specialization, e.g., \"Vice Principal\", \"PGT Biology\"), StartYear, EndYear, Description.\n\n     - TotalWorkExperienceInYears: Calculate from experiences. From 2005 to present, but with gaps. Start from first job 2005, end now. Experience mentioned as \"> 18 years\" in first line, so approximately 18 years. Calculate precisely.\n\n       - 2005-2010: 5 years\n\n       - 2010-2011: 1 year (PROGRESSIVE)\n\n       - 2011-2013: 2.5 years (NATIONAL)\n\n       - 2013-2020: 7 years (SHISHUKUNJ)\n\n       - 2020-2021: about 1 year (LABOUR INDIA, part online)\n\n       - 2021-present: current, say 3 years as of 2024, but date is Jan 2024, so from 2021 to 2024 is 3 years.\n\n       - Total approx 5+1****+7+1+3 = 19.5 years, round to 19 or use 18 as mentioned. But calculate based on dates.\n\n       - Better to sum the durations.\n\n5. **Skills:**\n\n   - From Page 4 and 5: Administrative Skills, Teaching Skills, listed as points.\n\n   - Also, in text: \"Competence in Information Technology: 1. MS-Office, Internet. 2. Using meeting applications - Zoom, Google meet. 3. G-Suite Trained\"\n\n   - Languages from Table-5: \"Hindi, English, Gujarati, Kannada\"\n\n   - Skills array should include professional skills like communication, management, etc.\n\n6. **Certifications:**\n\n   - From tables and text: UGC-NET, CTET, AWES, various diplomas.\n\n   - Also, workshops and trainings might fit here.\n\n7. **Achievements:**\n\n   - From text: Gold Medalist in M.Sc., certificates of participation, etc.\n\n   - Also in Table-4, seminars taken, but might be part of work experience.\n\n8. **Languages:**\n\n   - From Table-5: Hindi, English, Gujarati, Kannada.\n\n9. **Projects:**\n\n   - Not much mentioned. Perhaps from work descriptions, but no specific projects listed. So probably empty array.\n\nNow, for the JSON structure, I need to fill all required fields.\n\nStart with PersonalInformation:\n\n- FullName: \"Bhavana Gupta\"\n\n- FullNameEmbedding: null\n\n- Gender: Infer from name, \"Female\"\n\n- BirthDate: From Table-5, \"21 November 1979\" – format as \"1979-11-21\" or keep as string? Schema says format \"date\", so use ISO format if possible. But input is \"21 November 1979\", so \"1979-11-21\".\n\n- Email: \"<EMAIL>\"\n\n- Address: \"303, Mohini Mahal Apartment, 11 Indralok Colony, INDORE (M.P.) - 452009\"\n\n- City: \"Indore\"\n\n- State: \"Madhya Pradesh\" (M.P. expanded)\n\n- ContactNumber: \"9826974111\" or include both? Use \"9826974111\" as primary.\n\n- LinkedInProfile: null\n\nObjective: As extracted.\n\nEducation: List in order. First, highest degree.\n\n- M.Ed. 2022\n\n- M.Sc. 2003\n\n- B.Ed. 2001\n\n- B.Sc. 2000\n\n- 12th 1997\n\n- 10th 1995\n\n- Then diplomas: PGDSLM 2009, P.B.Ed.D.S.A. 2008, etc.\n\nFor Degree, match enum if possible. \"12th Pass\" for AISSCE, \"10th Pass\" for AISSE, \"MSc\" for M.Sc., etc.\n\nSpecialization: From tables.\n\nInstitution: Standardize, e.g., \"Devi Ahilya Vishwavidyalaya, Indore\" for DAVV.\n\nGraduationYear: Year part.\n\nGPA: Not given, but percentage is. Schema says if GPA mentioned, extract, else use percentage. But field is GPA and Percentage. Percentage is to be extracted or converted.\n\nSchema: \"Percentage\": \"Convert GPA to percentage using: (GPA / MaxGPA) * 100. If percentage is explicitly mentioned, extract it.\"\n\nHere percentage is mentioned, so use that. GPA and MaxGPA can be null if not mentioned.\n\nFor UGC-NET, it's a certification, not a degree, so perhaps put in Certifications.\n\nSchema has Education with specific degrees, and Certifications separate. UGC-NET is in the enum for Degree, so it can be in Education.\n\nBut in Table-2, it's listed separately. Better to put degrees in Education and qualifications like NET in Certifications.\n\nEnum includes \"UGC-NET Qualified\", so can put in Education.\n\nSimilarly for CTET, etc.\n\nBut schema for Education has \"Degree\" with that enum, so I'll include them.\n\nFor order, list from highest: PhD not present, so M.Ed., M.Sc., then B.Ed., B.Sc., then 12th, 10th, then diplomas if they fit.\n\nDiplomas might be lower, like \"D.El.Ed\" is in enum.\n\nP.B.Ed.D.S.A. not exact, but can use string.\n\nWorkExperience: List with latest first.\n\nTotalWorkExperienceInYears: Calculate sum of years.\n\nFrom dates:\n\n- Divine Child: 2005-06-08 to 2010-04-30: about 4.9 years\n\n- Progressive: 2010-07-01 to 2011-03-31: about 0.75 years\n\n- National Public: 2011-04-01 to 2013-10-31: about 2.58 years\n\n- Shishukunj: 2013-11-07 to 2020-10-31: about 6.98 years (wait, from Nov 2013 to Oct 2020 is almost 7 years)\n\nCalculate properly:\n\n- Divine Child: start 2005-06-08, end 2010-04-30: duration = 2010-04-30 - 2005-06-08 = 4 years 10 months approx 4.83 years\n\n- Progressive: 2010-07-01 to 2011-03-31: 8 months approx 0.67 years\n\n- National Public: 2011-04-01 to 2013-10-31: 2 years 6 months approx 2.5 years\n\n- Shishukunj: 2013-11-07 to 2020-10-31: 6 years 11 months approx 6.92 years\n\n- Labour India: 2020-09-14 to 2021-10-31: 1 year 1 month approx 1.08 years\n\n- Ideal Academy: 2021-01-01 to present (2024-01-29): about 3 years\n\nTotal: 4.83 + 0.67 + 2.5 + 6.92 + 1.08 + 3 = 18.99 years, so approximately 19 years. But text says \"> 18 years\", so use 19.\n\nSkills: From text, list them out.\n\nCertifications: UGC-NET, CTET, AWES, diplomas, workshops.\n\nAchievements: Gold Medalist, certificates.\n\nLanguages: From Table-5.\n\nProjects: Not specified, so empty.\n\nNow, for the JSON, I need to output a string.\n\nHandle embeddings as null.\n\nFinally, ensure no duplication."}}], "created": 1750944385, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 4781, "prompt_tokens": 12626, "total_tokens": 20785, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 3378, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4226, "text_tokens": 12626, "image_tokens": 0}, "num_sources_used": 0}}