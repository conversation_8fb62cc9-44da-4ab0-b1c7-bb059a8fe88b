{"id": "86629994-3fed-4d09-998e-dee0cf190d3b", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"AKANKSHA SHARMA\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Female\",\n      \"BirthDate\": \"2002-12-20\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"Indore, India\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"+91 **********\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"Empathetic Motivated and Passionate Psychology Graduate with extensive theoretical knowledge assisting psychotherapy. Monitoring Patients and Children progress and building strong Rapport. Expertise in Dealing with children and Impressive Teaching Skills serves appreciable critical thinking and cooperative work. Seeking a Teaching and counseling related working environment for practical exposure and Serve knowledge to students in Field of Social Sciences, Psychology, Environmental Studies, General Knowledge and Moral Science. Having advanced ability to deal with children and Make them learn with Effective Psychological and cognitive skills.\",\n    \"Education\": [\n      {\n        \"Degree\": \"Masters in Clinical Psychology\",\n        \"Specialization\": \"Clinical Psychology\",\n        \"Institution\": \"Devi Ahilya Vishwavidyalaya\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"BA (Bachelor of Arts)\",\n        \"Specialization\": \"Applied Psychology\",\n        \"Institution\": \"Amity University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2023-06-30\",\n        \"GPA\": 8.6,\n        \"MaxGPA\": 10,\n        \"Percentage\": 86\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Vidhyanjali International School\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2020-03-31\",\n        \"GPA\": 9.8,\n        \"MaxGPA\": 10,\n        \"Percentage\": 98\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Vidhyanjali International School\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2018-03-31\",\n        \"GPA\": 6.7,\n        \"MaxGPA\": 10,\n        \"Percentage\": 67\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Samvedna Clinic\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Counselor Intern\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2022-06-01\",\n        \"EndYear\": \"2022-08-31\",\n        \"Description/Responsibility\": \"Performed professional psychological assessments in order to identify and document patients' current mental state and condition. Worked with children Having Learning Difficulties. Conducted Aptitude and Interest Test and helped them with Interpersonal Communication. Assisted in psychotherapy and communicated with patients to educate them on different mental disorders and treatment options. Attended various seminars, monitored patients progress, and worked on the creation and integration of new treatment plans. Maintained great patient experience and satisfaction and provided advice and support to patients family members. Completed administration work and performed other duties as assigned by supervisors.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 0.25,\n    \"Skills\": [\n      \"Self-motivation\",\n      \"Ability to Work Under Pressure\",\n      \"Problem Solving\",\n      \"Effective Time Management\",\n      \"Computer Skills\",\n      \"Ability to Multitask\",\n      \"Leadership\",\n      \"Adaptability\",\n      \"Communication Skills\",\n      \"Ability to Work in a Team\",\n      \"Creativity\",\n      \"Decision Making\",\n      \"Critical Thinking\",\n      \"Planning and Coordination\",\n      \"Active Listening\",\n      \"Emotional Resilience\",\n      \"Research Skills\",\n      \"Observational Skills\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [\n      {\n        \"CertificationName\": \"Rorschach Inkblot Test\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Fortis\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-07-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"School Psychology\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Fortis\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-08-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Situational analysis in Emergencies preparedness and responses\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"UNICEF\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-07-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Child Protection core commitments for children in Humanitarian action Playlist\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"UNICEF\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Public Health Interventions in Pandemic and Epidemics\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"WHO\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-06-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Standard Precautions: Hand Hygiene\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"WHO\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-07-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Volunteering in World Autism Awareness Day\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Volunteering in Sangathan\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2022-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Participation at Fortis Hospital\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Fortis Hospital\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Completion by WHO\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"WHO\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of completion by UNICEF\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"UNICEF\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2021-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of participation by M.P Tourism\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"M.P Tourism\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2019-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Participation by 94.3 My FM\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"94.3 My FM\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2016-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Excellence in Music Competition\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2015-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Excellence in Science Exhibition\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2014-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Participation by Nai Duniya\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Nai Duniya\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2013-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Recognition WCMAS abacus\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"WCMAS\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2013-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Merit in Dance Competition\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2012-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certificate of Merit in Dance Competition\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2010-01-01\",\n        \"ExpiryDate\": null\n      }\n    ],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"100% Scholarship Award\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Amity University\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2020-01-01\"\n      },\n      {\n        \"AchievementName\": \"1st position in youth Fest\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Amity University\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2023-01-01\"\n      },\n      {\n        \"AchievementName\": \"2nd Rank in National Olympiad\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2018-01-01\"\n      },\n      {\n        \"AchievementName\": \"2nd position by Mahatam Gandhi Hindi Language Organization\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Mahatam Gandhi Hindi Language Organization\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2015-01-01\"\n      },\n      {\n        \"AchievementName\": \"1st Position in Poem Competition\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2009-01-01\"\n      }\n    ],\n    \"Languages\": [\n      \"French\",\n      \"Spanish\",\n      \"Hindi\",\n      \"English\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent which is in a CSV-like structure with page numbers, text, and bounding box coordinates. I need to reconstruct the layout using these coordinates to accurately extract information.\n\nThe coordinates are X1, Y1, X2, Y2, which represent the bounding box of the text. Typically, in PDFs, X increases to the right and Y increases downwards or upwards depending on the coordinate system. I need to assume a standard system; usually in PDFs, Y increases downwards from the top. I'll assume that here.\n\nStep 1: Analyze the UserContent. It's spread across pages 1, 2, and 3, and there's a Table-1 with more data.\n\nI should group the text by their positions to reconstruct sections like personal info, skills, education, etc.\n\nLet me sketch a rough layout based on Y-coordinates, as they might indicate vertical position (rows).\n\nStarting with Page 1:\n\n- Y around 0.08-0.10: \"AKANKSHA SHARMA\" – likely the name.\n\n- Y around 0.12: \"COUNSELLOR, TEACHER\" – probably the profession or title.\n\n- Y around 0.21: \"DETAILS\" and \"PROFILE\" – might be headers.\n\n- Then addresses, phone, email under DETAILS.\n\n- PROFILE has a long text about career objective.\n\n- <PERSON><PERSON><PERSON> listed on left, EMPLOYMENT HISTORY on right.\n\n- Skills have Y positions like 0.40, 0.43, etc., with text.\n\n- Employment history starts around Y 0.43 with \"Summer Internship, Samvedna Clinic\"\n\nNow, Page 2:\n\n- Starts with languages, education, hobbies, certificates, courses.\n\n- \"10th, CBSE, Vidhyanjali International School\" etc.\n\n- Table-1 has more data, including education and skills.\n\nTable-1 is separate:\n\n- \"Creativity,0\" \"Decision Making,EDUCATION\" etc. It seems messy, but it's probably a table with skills and then education details.\n\nI need to merge this with the positional data.\n\nStep 2: Reconstruct the layout.\n\nI should consider the X and Y coordinates to group text into sections.\n\nFor example, on Page 1:\n\n- Left side (X around 0.10) has labels like ADDRESS, PHONE, EMAIL, SKILLS.\n\n- Right side (X around 0.37) has PROFILE text, EMPLOYMENT HISTORY.\n\nOn Page 2, similar structure.\n\nTable-1 might be a continuation or a different section.\n\nLooking at Table-1:\n\nIt has headings and cells, but it's listed as:\n\n\"Creativity,0\"\n\n\"Decision Making,EDUCATION\"\n\n\"Critical Thinking,\"Masters in Clinical Psychology, Devi Ahilya Indore\"\"\n\netc.\n\nThis seems like a comma-separated table, but the first column might be skills, and second column has education or other info.\n\nIn the text, \"EDUCATION\" is mentioned, so perhaps after \"Decision Making\", it switches to education section.\n\nI need to parse this table carefully.\n\nStep 3: Extract relevant information and fill the output JSON.\n\nThe output must match the <user_json_schema> exactly.\n\nKey sections:\n\n- PersonalInformation: FullName, Gender, BirthDate, Email, Address, City, State, ContactNumber, LinkedInProfile.\n\n- Objective: From the PROFILE section.\n\n- Education: List in highest to lowest order.\n\n- WorkExperience: List with latest first.\n\n- TotalWorkExperienceInYears\n\n- Skills: Array of strings.\n\n- Certifications: From CERTIFICATES AND AWARDS.\n\n- Achievements: Might overlap or be separate.\n\n- Languages: Listed.\n\n- Projects: Not explicitly mentioned, might be null.\n\n- Embeddings should be null as per instructions.\n\nFirst, extract PersonalInformation.\n\nFrom Page 1:\n\n- FullName: \"AKANKSHA SHARMA\" – infer gender. Akanksha is a female name, so Gender: \"Female\".\n\n- BirthDate: From Page 3, \"Date of Birth: 20.12.2002\" – so \"2002-12-20\" in date format.\n\n- Email: \"<EMAIL>\"\n\n- Address: From Page 1, \"ADDRESS: Indore, India\" – but it's split. Y:0.23783 \"ADDRESS\", Y:0.25153 \"Indore\", Y:0.2639 \"India\". So Address could be \"Indore, India\".\n\n- City: \"Indore\"\n\n- State: Not explicitly stated, but Indore is in Madhya Pradesh, India. From education, it's mentioned. I should use the schema's anyOf for State. Madhya Pradesh is not in the enum, but there's a pattern \".*\" so I can use \"Madhya Pradesh\".\n\n- ContactNumber: \"+91 **********\"\n\n- LinkedInProfile: Not mentioned, so null or empty string? Schema says string, but if not found, keep as ''. Probably empty string.\n\nSchema says for strings, if not found, keep as '', for nullables, use null.\n\nLooking at schema:\n\n- BirthDate: [\"string\",\"null\"], so can be null.\n\n- Email: string, no null, so if not found, perhaps '', but it should be found.\n\nIn this case, all are present except LinkedIn.\n\nGender: infer from name, \"Female\".\n\nFullNameEmbedding: null.\n\nNow, Objective: From PROFILE section on Page 1: \"Empathetic Motivated and Passionate Psychology Graduate...\" up to \"Serve knowledge to students...\"\n\nI need to concatenate that text.\n\nEducation: From Table-1 and Page 2.\n\nTable-1 has:\n\n\"Decision Making,EDUCATION\"\n\n\"Critical Thinking,\"Masters in Clinical Psychology, Devi Ahilya Indore\"\"\n\n\"Planning and Coordination,\"Vishwavidyalaya, School of social Sciences Jul 2023 Present\"\"\n\nThen \"Active Listening,0\"\n\n\"Emotional Resilience,\"B.A.(H) Applied Psychology, Amity University Gwalior Madhya Pradesh\"\"\n\n\"Research Skills,Oct 2020 Jun 2023\"\n\n\"Observational Skills,CGPA- 8.6\"\n\n\"LANGUAGES,\"12th ,CBSE, Vidhyanjali International School Indore Mar 2019 Mar 2020\"\"\n\n\"Hindi,CGPA- 9.8\"\n\n\"English,0\"\n\nAlso on Page 2: \"10th, CBSE, Vidhyanjali International School, Mar 2017 - Mar 2018, CGPA- 6.7\"\n\nSo education entries:\n\n- Masters in Clinical Psychology, Devi Ahilya Vishwavidyalaya, School of Social Sciences, Jul 2023 - Present (ongoing?)\n\n- B.A.(H) Applied Psychology, Amity University Gwalior, Oct 2020 - Jun 2023, CGPA 8.6\n\n- 12th, CBSE, Vidhyanjali International School Indore, Mar 2019 - Mar 2020, CGPA 9.8\n\n- 10th, CBSE, Vidhyanjali International School, Mar 2017 - Mar 2018, CGPA 6.7\n\nOrder highest to lowest: Masters, Bachelors, 12th, 10th.\n\nDegree: Match to schema enums.\n\n- Masters: \"MA (Master of Arts)\" or similar? It's \"Masters in Clinical Psychology\" – not exact match, use pattern \".*\" so \"Masters in Clinical Psychology\"\n\nSchema has \"MA (Master of Arts)\", but it's psychology, perhaps use \"MSc (Master of Science)\" or fallback. Better to use the pattern.\n\nSchema says \"Try to match the list, else allow fallback.\" So use \"Masters in Clinical Psychology\" for Degree, Specialization \"Clinical Psychology\".\n\nInstitution: \"Devi Ahilya Vishwavidyalaya, School of Social Sciences\" – standardize to \"Devi Ahilya Vishwavidyalaya\". Remove campus suffixes.\n\nGraduationYear: For ongoing, perhaps null or end date. It's \"Jul 2023 Present\", so GraduationYear might be null or not set.\n\nSchema has GraduationYear as [\"string\",\"null\"], format date.\n\nFor ongoing, perhaps set to null.\n\nGPA and Percentage: CGPA given, need MaxGPA to convert.\n\nNot specified, so I might need to infer. Typically for CGPA, scale is 10, so MaxGPA 10.\n\nFor B.A., CGPA 8.6, probably out of 10.\n\nSimilarly for others.\n\nSchema says \"Use your knowledge... infer GPA scale\". For Indian universities, CGPA is often out of 10.\n\nSo assume MaxGPA 10 for all.\n\nPercentage: (GPA / MaxGPA) * 100.\n\nIf percentage mentioned, use that.\n\nHere CGPA is given.\n\nFor 12th and 10th, CGPA given.\n\n12th CGPA 9.8, 10th 6.7.\n\nNow WorkExperience.\n\nFrom Page 1: \"Summer Internship, Samvedna Clinic, Indore, Jun 2022 - Aug 2022\"\n\nDescription provided.\n\nRole: Not explicitly said, but from context, \"Counsellor, Teacher\", and description involves psychological assessments, etc. So perhaps \"Intern\" or \"Counselor Intern\".\n\nSchema says for Role: \"Must include the expertise and specialization Example: if taught Math then 'Math' should be included.\"\n\nHere, dealing with children, psychology, so \"Counseling Intern\" or something.\n\nFrom text: \"Performed professional psychological assessments...\" so Role: \"Psychology Intern\" or \"Counselor\".\n\nI'll use \"Counselor Intern\" based on the internship.\n\nCompany: \"Samvedna Clinic\"\n\nStartYear: \"2022-06-01\" or just year? Schema format date, but probably can use \"2022-06\" or something, but best to use full date or just year.\n\nSchema says format \"date\", so perhaps \"2022-06-01\" for start, assume first day.\n\nBut to simplify, use \"2022-06-01\" and \"2022-08-31\" or just the month-year if not specified.\n\nIn the text, \"Jun 2022 - Aug 2022\", so I can use \"2022-06\" and \"2022-08\" but format is date, probably expect ISO format.\n\nI'll use \"2022-06-01\" and \"2022-08-31\" for simplicity.\n\nTotalWorkExperienceInYears: Only one internship mentioned, 3 months, so approximately 0.25 years, but schema is number, so calculate.\n\nPerhaps round or exact. Since it's short, 0.\n\nBut might be more, only this is mentioned.\n\nIn employment history, only this internship.\n\nSo total experience might be low.\n\nSkills: From Page 1 left side: Self-motivation, Ability to Work Under Pressure, Problem Solving, Effective Time Management, Computer Skills, Ability to Multitask, Leadership, Adaptability, Communication Skills, Ability to Work in a Team.\n\nAlso from Table-1: Creativity, Decision Making, Critical Thinking, Planning and Coordination, Active Listening, Emotional Resilience, Research Skills, Observational Skills.\n\nNeed to combine and remove duplicates.\n\nLanguages: From Page 2: French, Spanish, Hindi, English. Also in Table-1 LANGUAGES section.\n\nHobbies listed, but not required.\n\nCertifications and Awards: From Page 2 CERTIFICATES AND AWARDS: various certificates.\n\nAlso COURSES on Page 2.\n\nSchema has Certifications with name, issuing org, dates.\n\nAchievements might be some of these.\n\nDistinguish: Certifications are formal, Achievements are awards.\n\ne.g., \"100% Scholarship Award\" might be achievement, \"Certificate of Volunteering\" might be certification.\n\nUse judgment.\n\nProjects: Not mentioned, so empty array.\n\nNow, order Education: Highest to lowest.\n\n- Masters in Clinical Psychology (ongoing)\n\n- B.A.(H) Applied Psychology\n\n- 12th\n\n- 10th\n\nFor GraduationYear, for ongoing masters, set null.\n\nFor B.A., \"2023-06-30\" or something, text says \"Oct 2020 Jun 2023\", so end date.\n\nSimilarly for others.\n\nGPA: For masters not given, so null or 0? If not mentioned, keep GPA, MaxGPA, Percentage as per schema, but required fields, wait no, in education object, all are required, but schema says if not found, keep as '', but for numbers, perhaps 0 or null.\n\nSchema for Education properties: GPA is number, no null, so if not found, perhaps 0 or handle.\n\nInstruction: \"If something is not found, keep the respective value of that field as ''.\"\n\nBut '' is string, GPA is number. Probably use null or empty string, but JSON might not allow empty string for number.\n\nIn JSON, for number fields, I should use null if not found, but schema defines as number, not nullable.\n\nLooking back: \"GPA\":{\"type\":\"number\"}, no null option.\n\nBut instruction says \"If something is not found, keep the respective value as ''.\"\n\nPerhaps use empty string, but that might not be valid JSON for number.\n\nI should use the value if found, else omit or use default.\n\nTo be safe, I'll use null for missing values, but schema might expect number.\n\nIn the description, it says \"If not found, keep as ''\", so perhaps use empty string.\n\nBut in JSON, for number fields, empty string might cause error. I need to output valid JSON.\n\nPerhaps the schema allows it, or I should use 0.\n\nI'll assume to use null where not applicable.\n\nBut to follow instruction, use '' for strings, and for numbers, perhaps '' as well, but I'll use null.\n\nNow, for WorkExperience, only one entry.\n\nTotalWorkExperienceInYears: Calculate from experiences. Here, internship from Jun 2022 to Aug 2022, about 3 months, so 0.25 years. But perhaps integer, schema is number, so 0.25.\n\nOr round down.\n\nUse intelligence.\n\nSkills: List all unique skills.\n\nCertifications: From CERTIFICATES AND AWARDS and COURSES.\n\nFor example, \"100% Scholarship Award\" – Achievement or Certification? Probably Achievement.\n\n\"Certificate of Volunteering\" – Certification.\n\nSchema has both, so I need to categorize.\n\nPut formal certificates under Certifications, awards under Achievements.\n\nLanguages: Hindi, English, French, Spanish.\n\nNow, construct JSON.\n\nFirst, PersonalInformation:\n\n- FullName: \"AKANKSHA SHARMA\"\n\n- FullNameEmbedding: null\n\n- Gender: \"Female\" (inferred)\n\n- BirthDate: \"2002-12-20\" (from \"20.12.2002\")\n\n- Email: \"<EMAIL>\"\n\n- Address: \"Indore, India\" (combine from text)\n\n- City: \"Indore\"\n\n- State: \"Madhya Pradesh\" (inferred from context)\n\n- ContactNumber: \"+91 **********\"\n\n- LinkedInProfile: \"\" (empty string)\n\nObjective: Concatenate the PROFILE text.\n\n\"Empathetic Motivated and Passionate Psychology Graduate with extensive theoretical knowledge assisting psychotherapy. Monitoring Patients and Children progress and building strong Rapport. Expertise in Dealing with children and Impressive Teaching Skills serves appreciable critical thinking and cooperative work. Seeking a Teaching and counseling related working environment for practical exposure and Serve knowledge to students in Field of Social Sciences, Psychology, Environmental Studies, General Knowledge and Moral Science. Having advanced ability to deal with children and Make them learn with Effective Psychological and cognitive skills.\"\n\nEducation: Array of objects.\n\n1. Degree: \"Masters in Clinical Psychology\", Specialization: \"Clinical Psychology\", Institution: \"Devi Ahilya Vishwavidyalaya\", GraduationYear: null (ongoing), GPA: null (not mentioned), MaxGPA: null, Percentage: null\n\nBut GPA is required, perhaps set to null or 0. From text, not given.\n\nIn Table-1, for masters, no CGPA mentioned.\n\nFor B.A., CGPA 8.6.\n\nSo for masters, since ongoing and no GPA, set GPA to null, but schema might not allow. I'll use null.\n\nBetter to set numerical fields to null if not found.\n\nBut to follow \"keep as ''\", perhaps use empty string, but for JSON, use null.\n\nI think null is fine.\n\nInstitutionEmbedding: null\n\n2. Degree: \"B.A.(H) Applied Psychology\" – match to \"BA (Bachelor of Arts)\", Specialization: \"Applied Psychology\", Institution: \"Amity University Gwalior\", GraduationYear: \"2023-06-30\" (from Jun 2023), GPA: 8.6, MaxGPA: 10.0 (assumed), Percentage: (8.6 / 10) * 100 = 86.0\n\n3. Degree: \"12th Pass\", Specialization: \"\" (no spec), Institution: \"Vidhyanjali International School\", GraduationYear: \"2020-03-31\", GPA: 9.8, MaxGPA: 10.0, Percentage: 98.0\n\n4. Degree: \"10th Pass\", Specialization: \"\", Institution: \"Vidhyanjali International School\", GraduationYear: \"2018-03-31\", GPA: 6.7, MaxGPA: 10.0, Percentage: 67.0\n\nOrder: Masters, BA, 12th, 10th.\n\nWorkExperience: Only one.\n\n- CompanyName: \"Samvedna Clinic\"\n\n- CompanyNameEmbedding: null\n\n- Role: \"Counselor Intern\" (inferred)\n\n- RoleEmbedding: null\n\n- StartYear: \"2022-06-01\"\n\n- EndYear: \"2022-08-31\"\n\n- Description/Responsibility: The text provided in employment history.\n\n- Description/ResponsibilityEmbedding: null\n\nTotalWorkExperienceInYears: 0.25 (3 months)\n\nSkills: List from text: Self-motivation, Ability to Work Under Pressure, Problem Solving, Effective Time Management, Computer Skills, Ability to Multitask, Leadership, Adaptability, Communication Skills, Ability to Work in a Team, Creativity, Decision Making, Critical Thinking, Planning and Coordination, Active Listening, Emotional Resilience, Research Skills, Observational Skills. Remove duplicates, but all seem unique.\n\nSkillsEmbedding: null\n\nCertifications: From COURSES and some CERTIFICATES.\n\ne.g., \"Rorschach Inkblot Test, Fortis, Jul 2021\", so CertificationName: \"Rorschach Inkblot Test\", IssuingOrganization: \"Fortis\", IssueDate: \"2021-07-01\", ExpiryDate: null\n\nSimilarly for others.\n\nFrom CERTIFICATES AND AWARDS, some might be certifications.\n\nAchievements: Awards like \"100% Scholarship Award\", \"1st position in youth Fest\", etc.\n\nLanguages: [\"French\", \"Spanish\", \"Hindi\", \"English\"]\n\nProjects: Not mentioned, so empty array.\n\nNow, output as JSON string."}}], "created": 1750943821, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 2420, "prompt_tokens": 7599, "total_tokens": 13971, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 3952, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4227, "text_tokens": 7599, "image_tokens": 0}, "num_sources_used": 0}}