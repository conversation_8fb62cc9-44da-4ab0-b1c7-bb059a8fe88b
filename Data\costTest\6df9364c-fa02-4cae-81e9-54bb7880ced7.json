{"id": "62dd6971-cfe8-43c6-868e-8f07bf327754", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"<PERSON><PERSON>\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Male\",\n      \"BirthDate\": \"1985-12-20\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"136 Radio Colony Indore\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"9826282172\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"To work in a learning environment where I can improve, utilize my existing knowledge, and help the organization grow.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MPEd (Master of Physical Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Barkatullah University Bhopal\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2018-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 65\n      },\n      {\n        \"Degree\": \"BPEd (Bachelor of Physical Education)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Barkatullah University Bhopal\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2013-01-01\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 72\n      },\n      {\n        \"Degree\": \"<PERSON><PERSON> (Bachelor of Commerce)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Vikram University\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 55\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Madhya Pradesh Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 58\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Madhya Pradesh Board\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 58\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Queens' College\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Sports Administrator\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Currently working as a Sports Administrator at Queens' College.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"The Shishukunj International School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Cricket Coach\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2014-01-01\",\n        \"EndYear\": \"2022-01-01\",\n        \"Description/Responsibility\": \"Worked as a Cricket Coach at The Shishukunj International School from 2014 till 2022.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"The Daly College Indore\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Cricket Coach\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2012-01-01\",\n        \"EndYear\": \"2015-01-01\",\n        \"Description/Responsibility\": \"Worked as a Cricket Coach at The Daly College Indore (Part-time) from 2012 to 2015.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Shri Ram Cricket Club\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Cricket Coach\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Worked as a Cricket Coach at Shri Ram Cricket Club.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 10,\n    \"Skills\": [\n      \"Off-break Bowler\",\n      \"Middle-order Batsman\",\n      \"Positive attitude\",\n      \"Hard Working\",\n      \"Dedication towards work\",\n      \"Badminton\",\n      \"Football\",\n      \"Squash\",\n      \"Swimming\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"Player in Indore Senior Divisional Cricket Team\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Indore Senior Divisional Cricket Team\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2010-01-01\"\n      },\n      {\n        \"AchievementName\": \"Player in Ujjain Senior Division Cricket Team\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Ujjain Senior Division Cricket Team\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2013-01-01\"\n      },\n      {\n        \"AchievementName\": \"Member of Indore Divisional U_19 Cricket Team\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Indore Divisional U_19 Cricket Team\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      },\n      {\n        \"AchievementName\": \"Captain of Men's Cricket Academy\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Men's Cricket Academy in L.N.I.P, Gwalior\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      }\n    ],\n    \"Languages\": [\n      \"English\",\n      \"Hindi\"\n    ],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is in a CSV-like structure with page numbers, text, and bounding box coordinates. I need to analyze this to reconstruct the layout and extract information for the resume.\n\nThe content spans three pages, with text and a couple of tables. I must use the bounding box coordinates (X1, Y1, X2, Y2) to understand the relative positions and group the text appropriately.\n\nLet me sketch out the layout based on the coordinates. The coordinates seem to be normalized, probably between 0 and 1, since values are like 0.11774, etc. Lower Y values might be at the top or bottom; I need to confirm the coordinate system. Typically in PDFs, Y=0 is at the bottom, but I should check the order.\n\nLooking at page 1:\n\n- \"RESUME\" at Y1=0.03138, probably top of the page.\n\n- Then \"Add.\" at Y1=0.21818, \"136 Radio Colony Indore\" at Y1=0.21835, similar Y, so probably on the same line or close.\n\n\"Add.\" X1=0.11774, Y1=0.21818, X2=0.19271, Y2=0.24033\n\n\"136 Radio Colony Indore\" X1=0.2979, Y1=0.21835, X2=0.69406, Y2=0.24672\n\nX coordinates: \"Add.\" starts at 0.11774, ends at 0.19271, and \"136 Radio Colony Indore\" starts at 0.2979, which is to the right, so likely \"Add.\" is a label, and the address is next to it.\n\nSimilarly, \"Cont. no.: 9826282172\" at Y1=0.25339\n\n\"E-mail: <EMAIL>\" at Y1=0.28779\n\nThen \"Carrier Objective\" at Y1=0.35715\n\nFollowed by the objective text.\n\nThen \"Educational Qualification: -\" at Y1=0.55515\n\nBut there's a table mentioned separately: Table-1 has educational details.\n\nAlso, on page 2 and 3, more sections.\n\nThere are two tables provided after the page content: Table-1 and Table-2.\n\nTable-1 is for education:\n\nM.PED,from BU Bhopal 2015 to 2018) - 65%\n\nB.PED,from BU Bhopal 2013) - 72%\n\nB.com,from Vikram University) - 55%\n\n12th,from MP Board) - 58%\n\n10th,from MP Board) - 58%\n\nTable-2 is personal info:\n\nName:,Devendra Singh Sisodiya\n\nFather's name:,lt. Prem Singh Sisodiya\n\nMarital status:,married\n\nGender:,Male.\n\nNationality:,Indian\n\nDate of birth:,20th Dec. 1985\n\nLanguage known:,English and Hindi\n\nOther Sports Knowledge:,\"Badminton, Football,\"\n\n\"Squash, Swimming\"\n\nNow, in the page content, there are sections like \"Current Experience\", \"Achievement\", \"Specialization\", \"Strength\", \"Personal information\", \"Declaration\".\n\nI need to reconstruct the layout. Since tables are given separately, I should integrate them based on the text references.\n\nFrom page 1, \"Educational Qualification: -\" is mentioned, and Table-1 likely corresponds to that.\n\nOn page 2, \"Current Experience: -\" is on page 1 at Y=0.85628, but the text starts on page 2.\n\nPage 1 has \"Current Experience: -\" at Y=0.85628, and page 2 starts with \"Currently working as a Sports Administrator at Queens' College.\"\n\nSo, the work experience is described in text on page 2.\n\nAchievements are on page 2, etc.\n\nPersonal information is on page 3, but Table-2 also has personal info.\n\nTable-2 has name, etc., and on page 3, there's \"Personal information\" section, but the text might be redundant or part of it.\n\nOn page 3, \"Personal information\" at Y=0.16893, but then \"Declaration\" and at the end \"Devendra Singh Sisodiya\" which might be the signature.\n\nTable-2 explicitly gives name, gender, etc.\n\nI should use the most reliable source. Since Table-2 is structured, it might be better to use that for personal info.\n\nBut the instruction is to reconstruct the layout from the given text and coordinates, and tables are provided separately.\n\nThe UserContent includes both the page text and the tables, so I need to consider all.\n\nNow, for extraction, I need to fill the JSON schema.\n\nStart with PersonalInformation.\n\nFrom Table-2:\n\n- Name: Devendra Singh Sisodiya\n\n- Gender: Male (explicitly stated)\n\n- BirthDate: 20th Dec. 1985 → should be in date format, probably \"1985-12-20\"\n\n- Email: from page 1, \"E-mail: <EMAIL>\"\n\n- Address: from page 1, \"Add. 136 Radio Colony Indore\" → so address is \"136 Radio Colony Indore\", city is Indore, state might be Madhya Pradesh (inferred from context, like MP Board in education)\n\nCity: Indore, which is in the list for cities.\n\nState: Madhya Pradesh, but it's not in the enum list provided. The enum has \"Madhya Pradesh\", yes it does.\n\nCheck the schema: State has enum including \"Madhya Pradesh\".\n\nContactNumber: from page 1, \"Cont. no.: 9826282172\"\n\nLinkedInProfile: not mentioned, so should be null or empty string? Schema says type string, but if not found, keep as ''. Probably empty string.\n\nSchema says \"If something is not found, keep the respective value as ''.\"\n\nAlso, FullNameEmbedding, etc., are to be left null, as per description.\n\nGender is explicitly \"Male\", so no inference needed.\n\nBirthDate: \"20th Dec. 1985\" → standardize to \"1985-12-20\" or keep as string? Schema says format \"date\", so probably \"1985-12-20\".\n\nNow, Objective: from page 1, \"Carrier Objective\" typo, should be \"Career Objective\", text is \"To work in a learning environment where I can improve, utilize my existing knowledge, and help the organization grow.\"\n\nSo, combine that.\n\nEducation: from Table-1.\n\nList is:\n\n- M.PED from BU Bhopal 2015 to 2018 - 65%\n\n- B.PED from BU Bhopal 2013 - 72% (probably 2013 is the year, but it's B.PED, might be graduation year)\n\nParse:\n\n\"M.PED,from BU Bhopal 2015 to 2018) - 65%\" → Degree: M.PED, which might be M.P.Ed (Master of Physical Education), Institution: BU Bhopal (probably Barkatullah University Bhopal), GraduationYear: 2018 (since 2015 to 2018), GPA or Percentage: 65%, so Percentage=65, GPA might not be mentioned, but schema requires GPA and MaxGPA. Percentage is given, so use that. GPA could be null or inferred, but better to set GPA as null if not mentioned, but schema has \"Percentage\": \"Convert GPA to percentage using: (GPA / MaxGPA) * 100. If percentage is explicitly mentioned, extract it.\" Here percentage is mentioned, so set Percentage=65. GPA and MaxGPA might be null, but schema makes them required. Check: in Education properties, all are required, including GPA, MaxGPA, Percentage.\n\nProblem: if percentage is given, but GPA not, what to do? Schema says \"GPA\":{\"type\":\"number\",\"description\":\"If mentioned, extract GPA value\"}, but it's required in the object. In the properties, it's defined, but in required array, it's listed. I need to provide a number, but if not mentioned, perhaps set to null, but type is number, not nullable. Schema says type \"number\", but in description \"If mentioned\", and earlier instruction \"If something is not found, keep as ''\", but '' is string, conflict.\n\nLook back: in the schema, for GPA, type is number, but for fields that can be null, it's defined as type [\"string\",\"null\"] or similar. For GPA, it's \"type\":\"number\", no null option. But instruction says \"If something is not found, keep the respective value as ''.\" Which is a string, but GPA is number. Inconsistency.\n\nIn the user_json_schema, for Education, properties have GPA as \"type\":\"number\", but for BirthDate, it's [\"string\",\"null\"]. For GPA, it's not nullable in type, but description says \"If mentioned\".\n\nI should set it to null if not found, but since type is number, perhaps use a default or something. But instruction overrides: \"If something is not found, keep the respective value as ''.\" So probably use empty string or null. To be safe, I'll use null where not found, as per some fields having [\"array\",\"null\"].\n\nIn the schema definition, for GPA, it's \"type\":\"number\", not specifying null, but I can output JSON with null.\n\nSimilarly for others.\n\nFor M.PED: Degree: probably \"M.P.Ed\" or match to \"MPEd (Master of Physical Education)\" from the enum list. Enum has \"MPEd (Master of Physical Education)\", so use that.\n\nSpecialization: not specified, so perhaps empty or infer from context. Context is sports, so maybe related, but not specified. Set to '' or null? Schema says type string, so ''.\n\nInstitution: \"BU Bhopal\" – standardize to full name. Probably Barkatullah University Bhopal. But schema says \"full, standardized name\", and \"return 'Sage University' instead of 'SU'\", so I should use \"Barkatullah University Bhopal\". I know that BU Bhopal is Barkatullah University.\n\nGraduationYear: 2018\n\nGPA: not mentioned, so perhaps null, but percentage is 65%. Schema has both GPA and Percentage. I can set GPA as null, Percentage as 65.\n\nBut schema has \"Convert GPA to percentage\", but here percentage is direct.\n\nSimilarly for others.\n\nOrder: Highest to lowest: M.PED, B.PED, B.com, 12th, 10th. M.PED is Masters level, B.PED Bachelor, etc.\n\nB.PED from 2013, but M.PED from 2015-2018, so chronologically later, but order should be by level: PhD > Masters > Bachelors > etc.\n\nSo M.PED first, then B.PED, etc.\n\nNow for each:\n\n- Degree: M.PED → \"MPEd (Master of Physical Education)\" or similar. Enum has \"MPEd (Master of Physical Education)\", yes.\n\nSpecialization: not given, so ''\n\nInstitution: \"Barkatullah University Bhopal\" (inferred)\n\nGraduationYear: \"2018\"\n\nGPA: null (not mentioned)\n\nMaxGPA: null\n\nPercentage: 65\n\nSimilarly for B.PED: \"B.PED,from BU Bhopal 2013) - 72%\" → Degree: \"BPEd (Bachelor of Physical Education)\" , Institution: \"Barkatullah University Bhopal\", GraduationYear: \"2013\", Percentage: 72\n\nB.com: \"B.com,from Vikram University) - 55%\" → Degree: \"BCom (Bachelor of Commerce)\", Institution: \"Vikram University\", GraduationYear: not specified, wait \"from Vikram University)\" no year, but in text it's just that. In Table-1, it's \"B.com,from Vikram University) - 55%\", no year. So GraduationYear should be null or ''. Set to null.\n\nSimilarly for 12th and 10th: no years, percentages given.\n\n12th: \"12th,from MP Board) - 58%\" → Degree: \"12th Pass\", Institution: \"MP Board\" probably Madhya Pradesh Board, but schema asks for institution name, so perhaps \"Madhya Pradesh Board\"\n\n10th: similar.\n\nNow WorkExperience: from page 2 text.\n\n- Currently working as a Sports Administrator at Queens' College. (ongoing, no end date)\n\n- Worked as a Cricket Coach at The Shishukunj International School from 2014 till 2022\n\n- Worked as a Cricket Coach at The Daly Collage Indore (Part-time) from 2012 to 2015 – probably typo, \"College\"\n\n- Worked as a Cricket Coach at Shri Ram Cricket Club. – no dates given.\n\nAlso, in the list, order should be latest first or by seniority. Schema says \"Show WorkExperience in Highest to Lowest order, like CEO > Manager > Intern as well as with latest work experience first\". A bit ambiguous. Probably sort by date, latest first, and within that, by role seniority if possible.\n\nRoles: Sports Administrator, Cricket Coach, Cricket Coach, Cricket Coach.\n\nCurrent is Sports Administrator, then Coach at School till 2022, etc.\n\nStart with current: Company: Queens' College, Role: Sports Administrator, StartYear: not specified, but current, so StartYear might be recent, but not given. Set StartYear and EndYear as null if not specified.\n\nFrom text: \"Currently working as a Sports Administrator at Queens' College.\" – no start date.\n\n\"Worked as a Cricket Coach at The Shishukunj International School from 2014 till 2022\" – StartYear: 2014, EndYear: 2022\n\n\"Worked as a Cricket Coach at The Daly Collage Indore (Part-time) from 2012 to 2015\" – Start: 2012, End: 2015\n\n\"Worked as a Cricket Coach at Shri Ram Cricket Club.\" – no dates, so StartYear and EndYear null.\n\nDescription/Responsibility: not detailed, so perhaps use the role or infer. Schema requires a string, so maybe set to a brief description based on role.\n\nFor example, for Sports Administrator: \"Administering sports activities at Queens' College.\"\n\nBut better to use what's available. Perhaps just the role description.\n\nIn the text, it's just stated, no further description, so set Description as the text or a summary.\n\nSchema has \"Description/Responsibility\": \"A description of the responsibilities and tasks performed in the role.\" Not much detail, so perhaps use \"Worked as Sports Administrator\" or something. I can use the text as is.\n\nTotalWorkExperienceInYears: need to calculate. From 2012 to 2015 (3 years), 2014 to 2022 (8 years, but overlap?), and current. There might be overlaps.\n\n2012-2015 at Daly College part-time, 2014-2022 at Shishukunj, so overlap, and current.\n\nAlso Shri Ram Cricket Club, no dates.\n\nApproximately, from 2012 onwards, but with overlaps, hard to calculate exact years. Perhaps sum the years without double-counting.\n\nFrom 2012 to 2015 at Daly (part-time), 2014 to 2022 at Shishukunj, current from some time.\n\nAssume non-overlapping or use max. Better to estimate based on mentioned periods.\n\nList the periods: Daly College: 2012-2015, Shishukunj: 2014-2022, Queens' College: current, say from 2022 or later.\n\nShishukunj till 2022, current at Queens', so perhaps started after 2022.\n\nNo start date for current.\n\nShri Ram: unknown.\n\nPerhaps total from first job: 2012 to now (2023 or whenever), but not precise.\n\nCalculate from known full-time or something. Since it's required, I need to estimate.\n\nFrom text: worked at Shishukunj from 2014 to 2022: 8 years, Daly part-time 2012-2015: perhaps not full count, current ongoing.\n\nSet to approximately 10 years or something, but better to calculate based on data.\n\nPerhaps sum the years: 2012-2015: 3 years, 2014-2022: 8 years, but overlap from 2014-2015, so total unique years: from 2012 to 2022 is 11 years, but part-time, and current additional.\n\nThis is messy. Perhaps use the longest or sum without overlap.\n\nSince it's a resume extraction, I can list the experiences and calculate total as sum of durations where dates are given.\n\nDaly: 2012-2015: 3 years, Shishukunj: 2014-2022: 8 years, overlap 2014-2015 (2 years), so add 3 + (8-2) = 9 years for those, plus current, but no duration. Shri Ram no duration.\n\nPerhaps set to 9 or 10. But schema requires a number, so I need to provide one. Maybe infer from context.\n\nCurrent is ongoing, and last end was 2022, so perhaps add current years, but no start. Set to the sum of known full periods.\n\nFor now, calculate based on Shishukunj 8 years, Daly 3 years part-time, perhaps count as 8 years main, or total 11 years from 2012.\n\nI think best is to use the years where dates are given without double-counting. From 2012 to 2022, 11 years, but part-time for some, and current role.\n\nPerhaps set TotalWorkExperienceInYears to 11, assuming continuous from 2012.\n\nBut there's a gap or overlap. Daly part-time 2012-2015, Shishukunj 2014-2022, so from 2012-2022 with work, 11 years.\n\nCurrent role started after, but \"currently working\", so perhaps ongoing from after 2022.\n\nText says \"Currently working as a Sports Administrator at Queens' College.\" and previously \"Worked at Shishukunj till 2022\", so probably started at Queens' after 2022.\n\nBut no start date, so total experience up to now includes current.\n\nFor simplicity, set to number of years from first job to now, but since no current year, perhaps use the end years.\n\nI can calculate the total duration from all entries.\n\nList:\n\n- Shri Ram Cricket Club: no dates, ignore for calculation or set duration 0.\n\n- Daly College: 2012-2015, 3 years\n\n- Shishukunj: 2014-2022, 8 years\n\n- Queens' College: current, say from 2022 to present, but no end, so ongoing.\n\nAssume current started in 2022, so from 2022 to now, but no year given. Perhaps the resume is from 2023 or later, but I don't know.\n\nSince it's extraction, perhaps sum the completed years: 3 + 8 = 11 years, and current is additional, but since no duration, set total to 11.\n\nOr use the latest end year minus earliest start year: 2022 - 2012 = 10 years, but not accurate.\n\nI think I'll set TotalWorkExperienceInYears to 10, as an estimate.\n\nNow Skills: from the text, in \"Strength: . Positive attitude, Hard Working, Dedication towards work\" on page 3, and also \"Specialization: Off-break Bowler, Middle-order Batsman\" on page 2, which might be skills.\n\nAlso, \"Other Sports Knowledge: Badminton, Football, Squash, Swimming\" from Table-2.\n\nSo skills could include: Positive attitude, Hard Working, Dedication, Off-break Bowler, Middle-order Batsman, Badminton, Football, Squash, Swimming.\n\nBut schema says \"professional skills\", so perhaps the sports-related are more relevant, or all.\n\nIn context, it's a sports administrator/coach, so include them.\n\nLanguages: from Table-2, \"Language known: English and Hindi\"\n\nCertifications: not mentioned explicitly, so empty array.\n\nAchievements: from page 2, \"Achievement: . As a Player in Indore Senior Divisional Cricket Team for the Years 2010-12, As a Player in Ujjain Senior Division Cricket Team 2013-14, Member of Indore Divisional U_19 Cricket Team., Captain of Men's Cricket Academy in (L.N.I.P), Gwalior\"\n\nSo list them as achievements.\n\nProjects: not mentioned, so empty.\n\nNow, for the JSON structure, I need to output in the specified format.\n\nFirst, PersonalInformation:\n\n- FullName: \"Devendra Singh Sisodiya\" from Table-2 or page 3 signature.\n\n- FullNameEmbedding: null\n\n- Gender: \"Male\" from Table-2\n\n- BirthDate: \"1985-12-20\" (standardize)\n\n- Email: \"<EMAIL>\"\n\n- Address: \"136 Radio Colony Indore\"\n\n- City: \"Indore\" (from address)\n\n- State: \"Madhya Pradesh\" (inferred from MP Board, etc.)\n\n- ContactNumber: \"9826282172\"\n\n- LinkedInProfile: '' (not mentioned)\n\nObjective: \"To work in a learning environment where I can improve, utilize my existing knowledge, and help the organization grow.\"\n\nEducation: array, ordered highest to lowest.\n\nDegrees:\n\n1. M.P.Ed - Masters level\n\n2. B.P.Ed - Bachelor level\n\n3. B.Com - Bachelor level, but different field\n\n4. 12th Pass\n\n5. 10th Pass\n\nOrder by level: M.P.Ed first, then B.P.Ed and B.Com both bachelors, perhaps group by level. Schema says \"PhD > Masters > Bachelors > Diploma > 12th > 10th\", so probably M.P.Ed, then B.P.Ed and B.Com, but no specification between them. Perhaps list M.P.Ed, B.P.Ed, B.Com, 12th, 10th.\n\nFor B.P.Ed and B.Com, both are bachelors, but in chronological order or by date. Better to follow the level strictly.\n\nNow for each:\n\n- M.P.Ed: Degree: \"MPEd (Master of Physical Education)\", Specialization: '' (or perhaps infer \"Physical Education\"), but not specified, so ''. Institution: \"Barkatullah University Bhopal\", GraduationYear: \"2018\", GPA: null, MaxGPA: null, Percentage: 65\n\n- B.P.Ed: Degree: \"BPEd (Bachelor of Physical Education)\", Specialization: '', Institution: \"Barkatullah University Bhopal\", GraduationYear: \"2013\", Percentage: 72\n\n- B.Com: Degree: \"BCom (Bachelor of Commerce)\", Specialization: '', Institution: \"Vikram University\", GraduationYear: null (not specified), Percentage: 55\n\n- 12th: Degree: \"12th Pass\", Institution: \"MP Board\" → probably \"Madhya Pradesh Board\", GraduationYear: null, Percentage: 58\n\n- 10th: similar, Degree: \"10th Pass\", Institution: \"Madhya Pradesh Board\", GraduationYear: null, Percentage: 58\n\nInstitutionEmbedding null.\n\nWorkExperience: array, order latest first.\n\nCurrent: Queens' College, Role: \"Sports Administrator\", StartYear: null, EndYear: null (ongoing), Description: \"Currently working as a Sports Administrator at Queens' College.\"\n\nThen Shishukunj: Company: \"The Shishukunj International School\", Role: \"Cricket Coach\", StartYear: \"2014\", EndYear: \"2022\", Description: \"Worked as a Cricket Coach\"\n\nDaly College: Company: \"The Daly College Indore\", Role: \"Cricket Coach\", StartYear: \"2012\", EndYear: \"2015\", Description: \"Worked as a Cricket Coach part-time\"\n\nShri Ram Cricket Club: Company: \"Shri Ram Cricket Club\", Role: \"Cricket Coach\", StartYear: null, EndYear: null, Description: \"Worked as a Cricket Coach\"\n\nOrder: latest first, so Queens' (current), then Shishukunj (ended 2022), then Daly (2012-2015), then Shri Ram (no date, perhaps earliest or last).\n\nSince Shri Ram no date, place it last or based on context. Probably assume it was before or concurrent, but put latest dated first.\n\nSo: Queens', Shishukunj, Daly, Shri Ram.\n\nTotalWorkExperienceInYears: estimate, say 10 or calculate. From 2012 to present, assume 11 years or so. But set to 10.\n\nSkills: list from strength and specialization: \"Off-break Bowler\", \"Middle-order Batsman\", \"Positive attitude\", \"Hard Working\", \"Dedication towards work\", and from other sports: \"Badminton\", \"Football\", \"Squash\", \"Swimming\". Perhaps keep as strings.\n\nSkillsEmbedding null.\n\nCertifications: none mentioned, so empty array.\n\nAchievements: from text:\n\n- As a Player in Indore Senior Divisional Cricket Team for 2010-12\n\n- As a Player in Ujjain Senior Division Cricket Team 2013-14\n\n- Member of Indore Divisional U_19 Cricket Team\n\n- Captain of Men's Cricket Academy in (L.N.I.P), Gwalior\n\nSo, AchievementName: e.g., \"Player in Indore Senior Divisional Cricket Team\", IssuingOrganization: perhaps \"Indore Divisional Cricket Team\" or inferred, IssueDate: from years.\n\nFor first: AchievementName: \"Player in Indore Senior Divisional Cricket Team\", IssuingOrganization: \"Indore Cricket Association\" or something, but not specified, so perhaps set to \"Indore Divisional Cricket Team\" or leave as per text.\n\nSchema requires IssuingOrganization, so use what's available, e.g., \"Indore Senior Divisional Cricket Team\" might be the organization.\n\nSimilarly, IssueDate: \"2010-12\" or specific years.\n\nBetter to set AchievementName and IssuingOrganization based on text.\n\nLanguages: from Table-2, \"English and Hindi\", so [\"English\", \"Hindi\"]\n\nProjects: none, empty array.\n\nNow, for embeddings, all should be null.\n\nOutput as JSON string.\n\nEnsure no duplicates, anticipate typos: e.g., \"Carrier Objective\" should be \"Career\", but in objective, use the text as is.\n\nInstitution names: standardize, e.g., \"BU Bhopal\" to \"Barkatullah University Bhopal\".\n\nSimilarly, \"The Daly Collage\" probably \"The Daly College\".\n\nFor education, institution for 12th and 10th: \"MP Board\" to \"Madhya Pradesh Board\".\n\nNow, compile the JSON."}}], "created": 1750943708, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 1273, "prompt_tokens": 5546, "total_tokens": 12447, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 5628, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4226, "text_tokens": 5546, "image_tokens": 0}, "num_sources_used": 0}}