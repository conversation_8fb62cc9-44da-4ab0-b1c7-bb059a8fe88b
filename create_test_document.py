#!/usr/bin/env python3
"""
Create a test DOCX document for testing conversion functionality.
"""

try:
    from docx import Document
    from docx.shared import Inches
    
    def create_test_docx():
        """Create a test DOCX document."""
        # Create a new document
        doc = Document()
        
        # Add a title
        title = doc.add_heading('Test Resume Document', 0)
        
        # Add personal information
        doc.add_heading('Personal Information', level=1)
        p = doc.add_paragraph()
        p.add_run('Name: ').bold = True
        p.add_run('<PERSON>')
        
        p = doc.add_paragraph()
        p.add_run('Email: ').bold = True
        p.add_run('<EMAIL>')
        
        p = doc.add_paragraph()
        p.add_run('Phone: ').bold = True
        p.add_run('+****************')
        
        # Add experience section
        doc.add_heading('Work Experience', level=1)
        
        doc.add_heading('Software Engineer - Tech Company', level=2)
        doc.add_paragraph('January 2020 - Present')
        doc.add_paragraph(
            'Developed and maintained web applications using Python, JavaScript, and React. '
            'Collaborated with cross-functional teams to deliver high-quality software solutions. '
            'Implemented automated testing and CI/CD pipelines.'
        )
        
        doc.add_heading('Junior Developer - Startup Inc.', level=2)
        doc.add_paragraph('June 2018 - December 2019')
        doc.add_paragraph(
            'Built responsive web interfaces and RESTful APIs. '
            'Participated in agile development processes and code reviews. '
            'Contributed to database design and optimization.'
        )
        
        # Add skills section
        doc.add_heading('Technical Skills', level=1)
        skills = [
            'Programming Languages: Python, JavaScript, Java, C++',
            'Web Technologies: HTML, CSS, React, Node.js, Django',
            'Databases: MySQL, PostgreSQL, MongoDB',
            'Tools: Git, Docker, AWS, Jenkins'
        ]
        
        for skill in skills:
            doc.add_paragraph(skill, style='List Bullet')
        
        # Add education section
        doc.add_heading('Education', level=1)
        doc.add_heading('Bachelor of Science in Computer Science', level=2)
        doc.add_paragraph('University of Technology, 2018')
        doc.add_paragraph('GPA: 3.8/4.0')
        
        # Save the document
        doc.save('test_document.docx')
        print("✅ Created test_document.docx successfully")
        
        return True
        
    if __name__ == "__main__":
        print("📝 Creating test DOCX document...")
        if create_test_docx():
            print("🎉 Test document created! You can now run the conversion tests.")
        else:
            print("❌ Failed to create test document")
            
except ImportError:
    print("❌ python-docx not available. Install it with: pip install python-docx")
except Exception as e:
    print(f"❌ Error creating test document: {e}")
