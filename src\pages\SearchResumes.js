import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { FiSearch, FiFilter, FiDownload, <PERSON>Eye, FiUser, FiMapPin, FiMail, FiPhone, FiExternalLink, FiClock, FiTrash2, FiHeart, FiCalendar, FiChevronDown } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ResumeModal from '../components/ResumeModal';
import { API_ENDPOINTS } from '../config/api';
import { formatPhoneNumber } from '../utils/phoneFormatter';
import { formatName, formatJobTitle } from '../utils/textFormatter';
import './SearchResumes.css';

const SearchResumes = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [filterQuery, setFilterQuery] = useState('');
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const resultsPerPage = 20;

  // Search History State
  const [searchHistory, setSearchHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [hasSearched, setHasSearched] = useState(false); // Track if user has performed a search
  const [lastSearchQuery, setLastSearchQuery] = useState(''); // Track the last searched query
  const [favorites, setFavorites] = useState({}); // Track favorite resumes

  // Advanced Filter states
  const [sortBy, setSortBy] = useState('name-asc'); // name-asc, name-desc, date-latest, date-earliest
  const [experienceFilter, setExperienceFilter] = useState('all'); // all, 0-2, 2-5, 5-10, 10+
  const [dateFilter, setDateFilter] = useState('all'); // all, today, week, month, custom
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [multipleFilters, setMultipleFilters] = useState(true); // Toggle between single and multiple filter modes
  const [matchType, setMatchType] = useState('similar'); // 'exact' or 'similar' for ChromaDB distance threshold
  const [debugInfo, setDebugInfo] = useState(null); // Store debug information from search
  const [showDebug, setShowDebug] = useState(false); // Toggle debug section visibility

  // Test localStorage function
  const testLocalStorage = () => {
    try {
      const testKey = 'test_storage';
      const testValue = 'test_data';
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);
      console.log('🧪 localStorage test:', retrieved === testValue ? 'PASSED' : 'FAILED');
      return retrieved === testValue;
    } catch (error) {
      console.error('🧪 localStorage test FAILED:', error);
      return false;
    }
  };

  // Load search history from backend API on component mount
  useEffect(() => {
    const loadSearchHistory = async () => {
      try {
        console.log('🔄 Loading search history from backend...');
        const response = await fetch(API_ENDPOINTS.SEARCH_HISTORY);

        if (response.ok) {
          const data = await response.json();
          const history = data.history || [];
          setSearchHistory(history);
          console.log('✅ Successfully loaded', history.length, 'search history items from backend');
        } else {
          console.log('⚠️ Backend search history not available, trying localStorage fallback');
          loadSearchHistoryFromLocalStorage();
        }
      } catch (error) {
        console.error('❌ Error loading search history from backend:', error);
        console.log('🔄 Falling back to localStorage...');
        loadSearchHistoryFromLocalStorage();
      }
    };

    const loadSearchHistoryFromLocalStorage = () => {
      try {
        console.log('🔄 Loading search history from localStorage...');
        const savedHistory = localStorage.getItem('resumeSearchHistory');

        if (savedHistory && savedHistory !== 'undefined' && savedHistory !== 'null' && savedHistory.trim() !== '') {
          const parsedHistory = JSON.parse(savedHistory);

          if (Array.isArray(parsedHistory) && parsedHistory.length >= 0) {
            setSearchHistory(parsedHistory);
            console.log('✅ Successfully loaded', parsedHistory.length, 'search history items from localStorage');
          } else {
            setSearchHistory([]);
          }
        } else {
          setSearchHistory([]);
        }
      } catch (error) {
        console.error('❌ Error loading search history from localStorage:', error);
        setSearchHistory([]);
      }
    };

    // Test localStorage first for fallback capability
    const isLocalStorageWorking = testLocalStorage();
    console.log('🧪 localStorage test result:', isLocalStorageWorking);

    // Always try to load from backend first
    loadSearchHistory();
    loadFavorites();
  }, []);

  // Load favorites from localStorage
  const loadFavorites = () => {
    try {
      const savedFavorites = localStorage.getItem('favoriteResumes');
      if (savedFavorites) {
        const parsedFavorites = JSON.parse(savedFavorites);
        setFavorites(parsedFavorites);
        console.log('✅ Loaded favorites:', Object.keys(parsedFavorites).length, 'items');
      }
    } catch (error) {
      console.error('❌ Error loading favorites:', error);
      setFavorites({});
    }
  };

  // Save search history to localStorage as fallback whenever it changes (with debounce)
  useEffect(() => {
    const saveHistoryToLocalStorage = () => {
      try {
        const historyString = JSON.stringify(searchHistory);
        localStorage.setItem('resumeSearchHistory', historyString);
        console.log('💾 Saved search history to localStorage as fallback:', searchHistory.length, 'items');
      } catch (error) {
        console.error('❌ Error saving search history to localStorage:', error);
      }
    };

    // Only save if we have a valid array (including empty array)
    if (Array.isArray(searchHistory)) {
      // Debounce the save operation
      const timeoutId = setTimeout(saveHistoryToLocalStorage, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [searchHistory]);

  // Close history dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showHistory && !event.target.closest('.history-dropdown')) {
        setShowHistory(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showHistory]);

  // Handle navigation state from activity log clicks
  useEffect(() => {
    if (location.state) {
      const { searchQuery: navSearchQuery, openHistoryTab, searchResults: navSearchResults, showResults } = location.state;

      if (openHistoryTab) {
        // Open history tab for search activity clicks
        setShowHistory(true);
        if (navSearchQuery) {
          setSearchQuery(navSearchQuery);
        }
      } else if (showResults && navSearchResults) {
        // Show search results for successful resume processing clicks
        setSearchResults(navSearchResults);
        setSearchQuery(navSearchQuery || '');
        setHasSearched(true);
        setTotalCount(navSearchResults.length);
        setTotalPages(Math.ceil(navSearchResults.length / resultsPerPage));
        toast.success(`Found resume for: ${navSearchQuery}`);
      }

      // Clear the navigation state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, resultsPerPage]);

  // Function to add search to history
  const addToSearchHistory = async (query, results, gptResponse, mongoQuery) => {
    console.log('🔍 addToSearchHistory called with:', { query, resultsCount: results?.length, gptResponse, mongoQuery });

    if (!query || query.trim() === '') {
      console.log('❌ Empty query, skipping search history');
      return;
    }

    const now = new Date();
    const historyItem = {
      id: `search_${now.getTime()}_${Math.random().toString(36).substr(2, 9)}`,
      query: query.trim(),
      timestamp: now.toISOString(),
      date: now.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }),
      time: now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }),
      resultsCount: results.length,
      gptResponse: gptResponse || '',
      mongoQuery: mongoQuery || [],
      results: results, // Store ALL results in MongoDB for instant loading
      previewResults: results.slice(0, 3) // Store first 3 results for preview in history display
    };

    console.log('📝 Creating search history item:', historyItem);

    // Update local state immediately
    setSearchHistory(prev => {
      // Keep ALL search queries including duplicates, just limit to last 50 searches
      const newHistory = [historyItem, ...prev].slice(0, 50);
      console.log('New history length:', newHistory.length);
      return newHistory;
    });

    // Save to backend API
    try {
      console.log('🌐 Attempting to save to backend API:', API_ENDPOINTS.SEARCH_HISTORY);
      const response = await fetch(API_ENDPOINTS.SEARCH_HISTORY, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(historyItem)
      });

      console.log('📡 Backend response status:', response.status);
      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ Search history saved to backend:', responseData);
      } else {
        const errorText = await response.text();
        console.log('⚠️ Failed to save search history to backend:', response.status, errorText);
        console.log('🔄 localStorage fallback active');
      }
    } catch (error) {
      console.error('❌ Error saving search history to backend:', error);
      console.log('🔄 localStorage fallback active');
    }
  };

  // Mock data for demonstration
  const mockResults = [
    {
      id: 1,
      name: 'John Doe',
      title: 'Senior Software Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      experience: '5+ years',
      skills: ['Python', 'React', 'Node.js', 'AWS'],
      education: 'MS Computer Science',
      summary: 'Experienced software engineer with expertise in full-stack development...',
      score: 95
    },
    {
      id: 2,
      name: 'Jane Smith',
      title: 'Frontend Developer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      experience: '3+ years',
      skills: ['React', 'TypeScript', 'CSS', 'JavaScript'],
      education: 'BS Computer Science',
      summary: 'Creative frontend developer passionate about user experience...',
      score: 88
    },
    {
      id: 3,
      name: 'Mike Johnson',
      title: 'DevOps Engineer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Austin, TX',
      experience: '4+ years',
      skills: ['Docker', 'Kubernetes', 'AWS', 'Python'],
      education: 'BS Information Technology',
      summary: 'DevOps engineer specializing in cloud infrastructure and automation...',
      score: 92
    }
  ];

  const handleSearch = async (e, queryOverride = null) => {
    if (e) e.preventDefault();
    const queryToSearch = queryOverride || searchQuery;
    if (!queryToSearch.trim()) {
      toast.warning('Please enter a search query');
      return;
    }

    setIsSearching(true);
    setHasSearched(true); // Mark that user has performed a search
    setLastSearchQuery(queryToSearch.trim()); // Track the last searched query

    try {
      // Call the backend_api.py search endpoint (port 8002) with match type
      const response = await fetch('http://192.168.1.15:8002/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: queryToSearch,
          match_type: matchType
        })
      });

      if (response.ok) {
        const data = await response.json();

        // Handle the new backend_api.py search response format
        let searchResults = [];
        let queryResult = 'Data received successfully.';

        if (data.results && Array.isArray(data.results)) {
          // New API returns { results: [...], gpt_response: "...", count: N }
          searchResults = data.results;
          queryResult = data.gpt_response || 'Data received successfully.';
        } else if (data.listOfDict && Array.isArray(data.listOfDict)) {
          // Fallback for old format
          searchResults = data.listOfDict;
          queryResult = data.result || 'Data received successfully.';
        } else if (Array.isArray(data)) {
          // Direct array response
          searchResults = data;
        }

        // Transform the resume data to display format
        const transformedResults = searchResults.map(resume => {
          // Handle the nested Resume structure from MongoDB
          const resumeData = resume.Resume || resume;
          const personalInfo = resumeData.PersonalInformation || {};
          const contactInfo = resumeData.ContactInformation || {};

          // Extract title with better logic (same as AllResumes.js)
          let title = 'Not Mentioned';
          if (personalInfo.Designation) {
            title = personalInfo.Designation;
          } else if (resumeData.WorkExperience && resumeData.WorkExperience.length > 0) {
            const latestJob = resumeData.WorkExperience[0];
            title = latestJob.Role || latestJob.JobTitle || latestJob.Position || 'Not Mentioned';
          } else if (resumeData.Objective) {
            // Try to extract title from objective
            const objectiveWords = resumeData.Objective.toLowerCase();
            if (objectiveWords.includes('engineer')) title = 'Engineer';
            else if (objectiveWords.includes('developer')) title = 'Developer';
            else if (objectiveWords.includes('manager')) title = 'Manager';
            else if (objectiveWords.includes('analyst')) title = 'Analyst';
            else if (objectiveWords.includes('consultant')) title = 'Consultant';
          }

          // Format the timestamp for display (same as AllResumes.js)
          const formatTimestamp = (timestamp) => {
            if (!timestamp) return 'Not Available';

            try {
              let date;
              if (timestamp.$date) {
                date = new Date(timestamp.$date);
              } else if (typeof timestamp === 'string') {
                date = new Date(timestamp);
              } else if (timestamp instanceof Date) {
                date = timestamp;
              } else {
                return 'Not Available';
              }

              if (isNaN(date.getTime())) {
                return 'Not Available';
              }

              return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              }) + ' at ' + date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              });
            } catch (error) {
              console.error('Error formatting timestamp:', error);
              return 'Not Available';
            }
          };

          const addedDate = formatTimestamp(resume.timestamp);

          return {
            id: resume._id || Math.random().toString(36).substr(2, 9),
            name: personalInfo.FullName || personalInfo.Name || 'Unknown',
            title: title,
            email: personalInfo.Email || contactInfo.Email || 'Not Mentioned',
            phone: personalInfo.ContactNumber || personalInfo.Phone || contactInfo.Phone || 'Not Mentioned',
            location: personalInfo.Address || contactInfo.Address || 'Not Mentioned',
            experience: resumeData.TotalWorkExperienceInYears ?
              `${resumeData.TotalWorkExperienceInYears} years` :
              resumeData.WorkExperience?.length ?
                `${resumeData.WorkExperience.length}+ positions` :
                'Not Mentioned',
            skills: resumeData.Skills || resumeData.TechnicalSkills || [],
            education: formatEducationDisplay(resumeData.Education),
            summary: resumeData.Objective || resumeData.Summary || 'No summary available',
            score: 95, // Default score for GPT search
            addedDate: addedDate, // Add the formatted timestamp
            rawData: resume // Keep original data for reference
          };
        });

        setSearchResults(transformedResults);
        setCurrentPage(1);
        setTotalCount(transformedResults.length);
        setTotalPages(1);

        // Update search query if using override
        if (queryOverride) {
          setSearchQuery(queryToSearch);
        }

        // Add to search history
        console.log('🔍 About to add to search history:', { queryToSearch, resultsCount: transformedResults.length, queryResult });
        const mongoQuery = data.mongo_query || data.query || [];
        addToSearchHistory(queryToSearch, transformedResults, queryResult, mongoQuery);
        console.log('✅ addToSearchHistory call completed');

        // Show appropriate message based on results and search method
        const searchMethod = data.api_status || 'unknown';
        const isChromaSearch = searchMethod === 'success';

        if (transformedResults.length === 0) {
          toast.warning(`🔍 No matches found for "${searchQuery}" (${matchType} match). Try different keywords or check spelling.`);
        } else if (queryResult && queryResult !== 'Data received successfully.') {
          const methodIcon = isChromaSearch ? '🧠' : '🤖';
          const methodText = isChromaSearch ? 'ChromaDB' : 'Legacy';
          toast.success(`${methodIcon} ${methodText} Search (${matchType} match): ${queryResult}`);
        } else {
          const methodIcon = isChromaSearch ? '🧠' : '🔍';
          const methodText = isChromaSearch ? 'ChromaDB' : 'Legacy';
          toast.success(`${methodIcon} Found ${transformedResults.length} matching resumes (${methodText} ${matchType} match)`);
        }

        // Developer mode output for new backend_api.py format
        console.log('Developer mode: API Response =', response.status, '|', JSON.stringify(mongoQuery));
        console.log('🤖 GPT Result:', queryResult);
        console.log('📊 MongoDB Query:', mongoQuery);
        console.log('📋 Search Results:', transformedResults.length, 'resumes found');
        console.log('🔧 API Status:', data.api_status || 'unknown');
        console.log('🎯 Match Type:', matchType, '| Distance Threshold:', data.distance_threshold || 'unknown');
        console.log('🧠 Search Method:', isChromaSearch ? 'ChromaDB Hybrid Search' : 'Legacy Search');

        // Log distance information if available
        if (transformedResults.length > 0 && transformedResults[0].rawData?._similarity_info) {
          const topDistance = transformedResults[0].rawData._similarity_info.match_distance;
          console.log('🏆 Top result distance:', topDistance, '(smaller = better match)');
          console.log('📊 Results ordered by distance (ascending)');

          // Log top 3 distances for debugging
          const topDistances = transformedResults.slice(0, 3).map((result, index) => {
            const distance = result.rawData?._similarity_info?.match_distance || 'N/A';
            return `#${index + 1}: ${distance}`;
          });
          console.log('🔢 Top 3 distances:', topDistances.join(', '));
        }

        // Store debug info for display
        setDebugInfo({
          query: searchQuery,
          match_type: matchType,
          api_status: data.api_status,
          distance_threshold: data.distance_threshold,
          debug_info: data.debug_info,
          gpt_response: queryResult,
          mongo_query: mongoQuery,
          results_count: transformedResults.length,
          search_method: isChromaSearch ? 'ChromaDB + MongoDB Hybrid' : 'Legacy Search',
          timestamp: new Date().toISOString()
        });

      } else {
        throw new Error(`API returned status ${response.status}`);
      }
    } catch (error) {
      console.error('Search error:', error);

      // Fallback exactly like frontendV3.py error handling (line 522)
      const fallbackResults = mockResults.filter(resume =>
        resume.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resume.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resume.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      );

      setSearchResults(fallbackResults);
      toast.error(`Error connecting to API: ${error.message}. Displaying sample data.`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleShowAll = async (page = 1) => {
    setIsSearching(true);
    setHasSearched(true); // Mark that user has performed a search
    setLastSearchQuery(''); // Clear last search query for "Show All"
    try {
      // Call the FastAPI backend exactly like frontendV3.py does (line 651)
      const response = await fetch('http://192.168.1.15:8002/showall');
      const data = await response.json();

      if (response.ok) {
        // Handle response exactly like frontendV3.py (lines 653-654)
        let transformedResults = [];

        if (Array.isArray(data)) {
          // FastAPI /showall returns array directly (like frontendV3.py expects)
          transformedResults = data.map(resume => {
            // Handle the nested Resume structure from MongoDB
            const resumeData = resume.Resume || resume;
            const personalInfo = resumeData.PersonalInformation || {};
            const contactInfo = resumeData.ContactInformation || {};

            // Extract title with better logic (same as AllResumes.js)
            let title = 'Not Mentioned';
            if (personalInfo.Designation) {
              title = personalInfo.Designation;
            } else if (resumeData.WorkExperience && resumeData.WorkExperience.length > 0) {
              const latestJob = resumeData.WorkExperience[0];
              title = latestJob.Role || latestJob.JobTitle || latestJob.Position || 'Not Mentioned';
            } else if (resumeData.Objective) {
              // Try to extract title from objective
              const objectiveWords = resumeData.Objective.toLowerCase();
              if (objectiveWords.includes('engineer')) title = 'Engineer';
              else if (objectiveWords.includes('developer')) title = 'Developer';
              else if (objectiveWords.includes('manager')) title = 'Manager';
              else if (objectiveWords.includes('analyst')) title = 'Analyst';
              else if (objectiveWords.includes('consultant')) title = 'Consultant';
            }

            // Format the timestamp for display (same as AllResumes.js)
            const formatTimestamp = (timestamp) => {
              if (!timestamp) return 'Not Available';

              try {
                let date;
                if (timestamp.$date) {
                  date = new Date(timestamp.$date);
                } else if (typeof timestamp === 'string') {
                  date = new Date(timestamp);
                } else if (timestamp instanceof Date) {
                  date = timestamp;
                } else {
                  return 'Not Available';
                }

                if (isNaN(date.getTime())) {
                  return 'Not Available';
                }

                return date.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                }) + ' at ' + date.toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                  hour12: true
                });
              } catch (error) {
                console.error('Error formatting timestamp:', error);
                return 'Not Available';
              }
            };

            const addedDate = formatTimestamp(resume.timestamp);

            return {
              id: resume._id || Math.random().toString(36).substr(2, 9),
              name: personalInfo.FullName || personalInfo.Name || 'Unknown',
              title: title,
              email: personalInfo.Email || contactInfo.Email || 'Not Mentioned',
              phone: personalInfo.ContactNumber || personalInfo.Phone || contactInfo.Phone || 'Not Mentioned',
              location: personalInfo.Address || contactInfo.Address || 'Not Mentioned',
              experience: resumeData.TotalWorkExperienceInYears ?
                `${resumeData.TotalWorkExperienceInYears} years` :
                resumeData.WorkExperience?.length ?
                  `${resumeData.WorkExperience.length}+ positions` :
                  'Not Mentioned',
              skills: resumeData.Skills || resumeData.TechnicalSkills || [],
              education: formatEducationDisplay(resumeData.Education),
              summary: resumeData.Objective || resumeData.Summary || 'No summary available',
              score: 90, // Default score for show all
              addedDate: addedDate, // Add the formatted timestamp
              rawData: resume // Keep original data for reference
            };
          });
        } else if (data.error) {
          throw new Error(data.error);
        } else {
          // Fallback to mock data if API doesn't return proper results
          transformedResults = mockResults;
        }

        setSearchResults(transformedResults);
        setCurrentPage(1);
        setTotalCount(transformedResults.length);
        setTotalPages(Math.ceil(transformedResults.length / resultsPerPage));
        setSearchQuery('');

        toast.success(`📊 Displaying all ${transformedResults.length} resumes from database`);
      } else {
        throw new Error(data.error || 'Failed to load resumes');
      }
    } catch (error) {
      console.error('Load resumes error:', error);

      // Fallback to mock data on network error (like frontendV3.py)
      setSearchResults(mockResults);
      setCurrentPage(1);
      setTotalCount(mockResults.length);
      setTotalPages(Math.ceil(mockResults.length / resultsPerPage));
      setSearchQuery('');

      toast.warning(`Using demo data - Showing ${mockResults.length} sample resumes`);
    } finally {
      setIsSearching(false);
    }
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      handleShowAll(page);
    }
  };

  // Helper function to format education with specialization
  const formatEducationDisplay = (education) => {
    if (!education || !Array.isArray(education) || education.length === 0) {
      return 'Not Mentioned';
    }

    const firstEdu = education[0];
    if (!firstEdu) return 'Not Mentioned';

    let educationText = firstEdu.Degree || 'Degree';

    // Add specialization if available
    if (firstEdu.Specialization) {
      educationText += ` in ${firstEdu.Specialization}`;
    } else if (firstEdu.Field) {
      educationText += ` in ${firstEdu.Field}`;
    } else if (firstEdu.Subject) {
      educationText += ` in ${firstEdu.Subject}`;
    }

    // Add institution if available
    if (firstEdu.Institution || firstEdu.School) {
      educationText += ` from ${firstEdu.Institution || firstEdu.School}`;
    }

    return educationText;
  };

  // Helper function to extract experience years from resume data
  const getExperienceYears = (resume) => {
    if (resume.rawData?.Resume?.TotalWorkExperienceInYears) {
      return parseInt(resume.rawData.Resume.TotalWorkExperienceInYears);
    }

    // Try to extract from experience display text
    const expText = resume.experience || '';
    const match = expText.match(/(\d+)/);
    if (match) {
      return parseInt(match[1]);
    }

    return 0;
  };

  // Helper function to parse date from addedDate string
  const parseAddedDate = (dateString) => {
    if (!dateString || dateString === 'Not Available') return null;

    try {
      // Parse "Dec 6, 2024 at 1:30 PM" format
      const parts = dateString.split(' at ');
      if (parts.length === 2) {
        return new Date(parts[0] + ' ' + parts[1]);
      }
      return new Date(dateString);
    } catch (error) {
      return null;
    }
  };

  // Generate filter context text
  const getFilterContext = () => {
    const contexts = [];

    // Add sorting context
    if (sortBy === 'name-asc') {
      contexts.push('Sorted by Name (A-Z)');
    } else if (sortBy === 'name-desc') {
      contexts.push('Sorted by Name (Z-A)');
    } else if (sortBy === 'date-latest') {
      contexts.push('Sorted by Date (Latest First)');
    } else if (sortBy === 'date-earliest') {
      contexts.push('Sorted by Date (Earliest First)');
    }

    // Add experience filter context
    if (experienceFilter !== 'all') {
      const expLabels = {
        '0-2': '0-2 years experience',
        '2-5': '2-5 years experience',
        '5-10': '5-10 years experience',
        '10+': '10+ years experience'
      };
      contexts.push(`Filtered by ${expLabels[experienceFilter]}`);
    }

    // Add date filter context
    if (dateFilter !== 'all') {
      const dateLabels = {
        'today': 'Added today',
        'week': 'Added in last week',
        'month': 'Added in last month',
        'custom': customDateFrom && customDateTo ?
          `Added between ${new Date(customDateFrom).toLocaleDateString()} and ${new Date(customDateTo).toLocaleDateString()}` :
          'Custom date range'
      };
      contexts.push(`Filtered by ${dateLabels[dateFilter]}`);
    }

    return contexts;
  };

  // Filter and sort search results
  const getFilteredAndSortedResults = () => {
    let filtered = searchResults.filter(result => {
      if (!filterQuery) return true;
      return result.name.toLowerCase().includes(filterQuery.toLowerCase()) ||
             result.title.toLowerCase().includes(filterQuery.toLowerCase()) ||
             result.location.toLowerCase().includes(filterQuery.toLowerCase());
    });

    // In single filter mode, only apply the most recently changed filter
    if (!multipleFilters) {
      // Apply only one filter based on what was last changed
      // For now, we'll prioritize in order: experience, date, then sort
      if (experienceFilter !== 'all') {
        filtered = filtered.filter(result => {
          const years = getExperienceYears(result);
          switch (experienceFilter) {
            case '0-2': return years >= 0 && years <= 2;
            case '2-5': return years > 2 && years <= 5;
            case '5-10': return years > 5 && years <= 10;
            case '10+': return years > 10;
            default: return true;
          }
        });
      } else if (dateFilter !== 'all') {
        const now = new Date();
        filtered = filtered.filter(result => {
          const resultDate = parseAddedDate(result.addedDate);
          if (!resultDate) return false;

          switch (dateFilter) {
            case 'today':
              return resultDate.toDateString() === now.toDateString();
            case 'week':
              const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              return resultDate >= weekAgo;
            case 'month':
              const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
              return resultDate >= monthAgo;
            case 'custom':
              if (customDateFrom && customDateTo) {
                const fromDate = new Date(customDateFrom);
                const toDate = new Date(customDateTo);
                return resultDate >= fromDate && resultDate <= toDate;
              }
              return true;
            default:
              return true;
          }
        });
      }
    } else {
      // Multiple filters mode - apply all filters
      // Apply experience filter
      if (experienceFilter !== 'all') {
        filtered = filtered.filter(result => {
          const years = getExperienceYears(result);
          switch (experienceFilter) {
            case '0-2': return years >= 0 && years <= 2;
            case '2-5': return years > 2 && years <= 5;
            case '5-10': return years > 5 && years <= 10;
            case '10+': return years > 10;
            default: return true;
          }
        });
      }

      // Apply date filter
      if (dateFilter !== 'all') {
        const now = new Date();
        filtered = filtered.filter(result => {
          const resultDate = parseAddedDate(result.addedDate);
          if (!resultDate) return false;

          switch (dateFilter) {
            case 'today':
              return resultDate.toDateString() === now.toDateString();
            case 'week':
              const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              return resultDate >= weekAgo;
            case 'month':
              const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
              return resultDate >= monthAgo;
            case 'custom':
              if (customDateFrom && customDateTo) {
                const fromDate = new Date(customDateFrom);
                const toDate = new Date(customDateTo);
                return resultDate >= fromDate && resultDate <= toDate;
              }
              return true;
            default:
              return true;
          }
        });
      }
    }

    // Apply sorting (always applied regardless of filter mode)
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'date-latest':
          const dateA = parseAddedDate(a.addedDate);
          const dateB = parseAddedDate(b.addedDate);
          if (!dateA && !dateB) return 0;
          if (!dateA) return 1;
          if (!dateB) return -1;
          return dateB.getTime() - dateA.getTime();
        case 'date-earliest':
          const dateA2 = parseAddedDate(a.addedDate);
          const dateB2 = parseAddedDate(b.addedDate);
          if (!dateA2 && !dateB2) return 0;
          if (!dateA2) return 1;
          if (!dateB2) return -1;
          return dateA2.getTime() - dateB2.getTime();
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredResults = getFilteredAndSortedResults();

  const handleDownload = async (resume) => {
    try {
      // Download the original PDF from MongoDB only
      const resumeId = resume._id || resume.id;

      if (!resumeId) {
        toast.error('Resume ID not found. Cannot download PDF.');
        return;
      }

      try {
        const response = await fetch(`http://192.168.1.15:8002/api/resumes/${resumeId}/download`);

        if (response.ok) {
          // PDF download successful
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // Get filename from response headers or use default
          const contentDisposition = response.headers.get('Content-Disposition');
          let filename = `${resume.name.replace(/\s+/g, '_')}_Resume.pdf`;

          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1].replace(/['"]/g, '');
            }
          }

          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          toast.success(`Downloaded PDF for ${resume.name}`);
          return;
        } else {
          // Handle error response
          const errorData = await response.json();
          toast.error(`Failed to download PDF: ${errorData.error || 'Unknown error'}`);
          return;
        }
      } catch (pdfError) {
        console.error('PDF download failed:', pdfError);
        toast.error(`Failed to download PDF: ${pdfError.message}`);
        return;
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download resume. Please try again.');
    }
  };

  const handleView = (resume) => {
    setSelectedResume(resume);
    setIsModalOpen(true);
    toast.info(`Opening detailed view for ${resume.name}`);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const handleEmailClick = (email, name) => {
    window.open(`mailto:${email}?subject=Regarding your resume&body=Hi ${name},%0D%0A%0D%0AI found your resume and would like to discuss potential opportunities.%0D%0A%0D%0ABest regards`, '_blank');
    toast.info(`Opening email client to contact ${name}`);
  };

  const handlePhoneClick = (phone, name) => {
    if (navigator.userAgent.match(/Mobile|Android|iPhone|iPad/)) {
      window.open(`tel:${phone}`, '_blank');
    } else {
      navigator.clipboard.writeText(phone).then(() => {
        toast.success(`Phone number copied: ${phone}`);
      }).catch(() => {
        toast.info(`Phone: ${phone}`);
      });
    }
  };

  // Toggle favorite status
  const toggleFavorite = (resume) => {
    try {
      const currentFavorites = { ...favorites };
      const resumeId = resume.id;

      if (currentFavorites[resumeId]) {
        // Remove from favorites
        delete currentFavorites[resumeId];
        toast.success(`Removed ${resume.name} from favorites`);
      } else {
        // Add to favorites
        const favoriteData = {
          ...resume,
          dateAdded: new Date().toISOString(),
          rawData: resume.rawData // Keep original data for download
        };
        currentFavorites[resumeId] = favoriteData;
        toast.success(`Added ${resume.name} to favorites`);
      }

      // Update state and localStorage
      setFavorites(currentFavorites);
      localStorage.setItem('favoriteResumes', JSON.stringify(currentFavorites));

      // Dispatch event to update header count
      window.dispatchEvent(new Event('favoritesUpdated'));

      console.log('💖 Updated favorites:', Object.keys(currentFavorites).length, 'items');
    } catch (error) {
      console.error('❌ Error toggling favorite:', error);
      toast.error('Error updating favorites');
    }
  };



  // Search History Functions
  const handleHistorySearch = async (historyItem) => {
    try {
      setSearchQuery(historyItem.query);
      setShowHistory(false);

      // Check if we have all results stored in the history item
      if (historyItem.results && historyItem.results.length === historyItem.resultsCount) {
        // Use stored results directly
        setSearchResults(historyItem.results);
        setTotalCount(historyItem.resultsCount);
        setCurrentPage(1);
        setTotalPages(Math.ceil(historyItem.resultsCount / resultsPerPage));
        setHasSearched(true);
        toast.success(`📋 Loaded ${historyItem.resultsCount} results from history: "${historyItem.query}"`);
        return;
      }

      // If results are not complete in history, fetch from backend
      console.log('🔄 Fetching full search results from backend for history item:', historyItem.id);
      const response = await fetch(`${API_ENDPOINTS.SEARCH_HISTORY}/${historyItem.id}/results`);

      if (response.ok) {
        const data = await response.json();
        const fullResults = data.results || [];

        setSearchResults(fullResults);
        setTotalCount(fullResults.length);
        setCurrentPage(1);
        setTotalPages(Math.ceil(fullResults.length / resultsPerPage));
        setHasSearched(true);
        toast.success(`📋 Loaded ${fullResults.length} results from database: "${historyItem.query}"`);
      } else {
        // Fallback: re-execute the search if backend fetch fails
        console.log('⚠️ Failed to fetch results from backend, re-executing search');
        toast.info(`🔄 Re-searching for: "${historyItem.query}"`);
        await handleSearch(null, historyItem.query);
      }
    } catch (error) {
      console.error('❌ Error loading search history:', error);
      // Fallback: re-execute the search
      toast.info(`🔄 Re-searching for: "${historyItem.query}"`);
      await handleSearch(null, historyItem.query);
    }
  };

  const clearSearchHistory = async () => {
    try {
      // Clear from backend first
      const response = await fetch(API_ENDPOINTS.SEARCH_HISTORY, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('✅ Search history cleared from backend');
      } else {
        console.log('⚠️ Failed to clear search history from backend');
      }
    } catch (error) {
      console.error('❌ Error clearing search history from backend:', error);
    }

    // Clear local state and localStorage regardless
    setSearchHistory([]);
    localStorage.removeItem('resumeSearchHistory');
    console.log('Search history cleared');
    toast.success('Search history cleared');
  };

  const deleteHistoryItem = async (itemId) => {
    try {
      // Delete from backend first
      const response = await fetch(`${API_ENDPOINTS.SEARCH_HISTORY}/${itemId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        console.log('✅ Search history item deleted from backend');
      } else {
        console.log('⚠️ Failed to delete search history item from backend');
      }
    } catch (error) {
      console.error('❌ Error deleting search history item from backend:', error);
    }

    // Update local state regardless
    setSearchHistory(prev => prev.filter(item => item.id !== itemId));
    toast.success('Search removed from history');
  };

  const formatTimeAgo = (timestamp, date, time) => {
    // If we have pre-formatted date and time, use them for recent searches
    if (date && time) {
      const now = new Date();
      const searchTime = new Date(timestamp);
      const diffInMinutes = Math.floor((now - searchTime) / (1000 * 60));

      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      if (diffInMinutes < 1440) { // Less than 24 hours
        const diffInHours = Math.floor(diffInMinutes / 60);
        return `${diffInHours}h ago`;
      }

      // For older searches, show full date and time
      return `${date} at ${time}`;
    }

    // Fallback for old history items without formatted date/time
    const now = new Date();
    const searchTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - searchTime) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return searchTime.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleExportAll = () => {
    if (filteredResults.length === 0) {
      toast.warning('No results to export');
      return;
    }

    try {
      const exportData = filteredResults.map(resume => ({
        name: resume.name,
        title: resume.title,
        email: resume.email,
        phone: resume.phone,
        location: resume.location,
        experience: resume.experience,
        education: resume.education,
        skills: resume.skills.join(', '),
        summary: resume.summary,
        matchScore: resume.score
      }));

      const csvContent = [
        'Name,Title,Email,Phone,Location,Experience,Education,Skills,Summary,Match Score',
        ...exportData.map(row =>
          Object.values(row).map(value =>
            `"${String(value).replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Resume_Search_Results_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Exported ${filteredResults.length} resumes to CSV`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export results. Please try again.');
    }
  };

  return (
    <div className="search-page">
      <div className="page-header">
        <h1>Search Resumes</h1>
        <p>Use natural language to find the perfect candidates for your needs</p>
      </div>

      {/* Search Section */}
      <div className="search-section">
        <form onSubmit={handleSearch} className="search-form">
          <div className="search-input-group">
            <div className="search-input-wrapper">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="e.g., 'Find Python developers with 5+ years experience' or 'Show frontend engineers in California'"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search-input"
                disabled={isSearching}
              />
            </div>

            {/* Match Type Toggle */}
            <div className="match-type-toggle">
              <label className="match-type-label">Search Type:</label>
              <div className="toggle-buttons">
                <button
                  type="button"
                  className={`toggle-btn ${matchType === 'exact' ? 'active' : ''}`}
                  onClick={() => setMatchType('exact')}
                  title="Exact match (distance threshold: 0.5)"
                >
                  Exact Match
                </button>
                <button
                  type="button"
                  className={`toggle-btn ${matchType === 'similar' ? 'active' : ''}`}
                  onClick={() => setMatchType('similar')}
                  title="Similar match (distance threshold: 2.0)"
                >
                  Similar Match
                </button>
              </div>
            </div>

            <div className="search-buttons">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSearching}
              >
                {isSearching ? (
                  <>
                    <div className="loading-spinner" />
                    Searching...
                  </>
                ) : (
                  <>
                    <FiSearch size={16} />
                    Search
                  </>
                )}
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleShowAll}
                disabled={isSearching}
              >
                Show All
              </button>
              <div className="history-dropdown">
                <button
                  type="button"
                  className="btn btn-secondary history-btn"
                  onClick={() => setShowHistory(!showHistory)}
                  disabled={isSearching}
                  title="Search History"
                >
                  <FiClock size={16} />
                  History ({searchHistory.length})
                </button>
                {showHistory && (
                  <div className="history-dropdown-content">
                    <div className="history-header">
                      <h4>Search History</h4>
                      <div className="history-actions">
                        {searchHistory.length > 0 && (
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={clearSearchHistory}
                            title="Clear all history"
                          >
                            <FiTrash2 size={14} />
                          </button>
                        )}
                        <button
                          className="btn btn-sm btn-secondary"
                          onClick={() => {
                            console.log('Current history:', searchHistory);
                            console.log('localStorage content:', localStorage.getItem('resumeSearchHistory'));
                            testLocalStorage();
                          }}
                          title="Debug localStorage"
                        >
                          Debug
                        </button>
                      </div>
                    </div>
                    <div className="history-list">
                      {searchHistory.length === 0 ? (
                        <div className="history-empty">
                          <p>No search history yet</p>
                        </div>
                      ) : (
                        searchHistory.map((item) => (
                          <div key={item.id} className="history-item">
                            <div className="history-item-content" onClick={() => handleHistorySearch(item)}>
                              <div className="history-query">{item.query}</div>
                              <div className="history-meta">
                                <span className="history-time">
                                  <FiClock size={12} />
                                  {formatTimeAgo(item.timestamp, item.date, item.time)}
                                </span>
                                <span className="history-results">
                                  {item.resultsCount} results
                                </span>
                              </div>
                              {item.gptResponse && item.gptResponse !== 'Data received successfully.' && (
                                <div className="history-gpt-response">
                                  {item.gptResponse}
                                </div>
                              )}
                            </div>
                            <button
                              className="history-delete-btn"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteHistoryItem(item.id);
                              }}
                              title="Remove from history"
                            >
                              <FiTrash2 size={12} />
                            </button>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>

        {/* Filter Section */}
        {searchResults.length > 0 && (
          <div className="filter-section">
            <div className="filter-input-wrapper">
              <FiFilter className="filter-icon" />
              <input
                type="text"
                placeholder="Filter results by name, title, or location..."
                value={filterQuery}
                onChange={(e) => setFilterQuery(e.target.value)}
                className="filter-input"
              />
            </div>
            <div className="filter-controls">
              <button
                className="btn btn-tertiary advanced-filter-btn"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                title="Toggle advanced filters"
              >
                <FiFilter size={16} />
                Advanced Filters
                <FiChevronDown
                  size={14}
                  className={`filter-chevron ${showAdvancedFilters ? 'rotated' : ''}`}
                />
              </button>
              <div className="results-count">
                {filteredResults.length} of {searchResults.length} results
              </div>
            </div>
          </div>
        )}

        {/* Advanced Filter Panel */}
        {searchResults.length > 0 && showAdvancedFilters && (
          <div className="advanced-filter-panel">
            {/* Filter Mode Toggle */}
            <div className="filter-mode-section">
              <div className="filter-mode-toggle">
                <label className="filter-mode-label">Filter Mode:</label>
                <div className="toggle-buttons">
                  <button
                    className={`toggle-btn ${multipleFilters ? 'active' : ''}`}
                    onClick={() => setMultipleFilters(true)}
                  >
                    Multiple Filters
                  </button>
                  <button
                    className={`toggle-btn ${!multipleFilters ? 'active' : ''}`}
                    onClick={() => setMultipleFilters(false)}
                  >
                    Single Filter
                  </button>
                </div>
              </div>
            </div>

            <div className="filter-row">
              <div className="filter-group">
                <label className="filter-label">Sort by Name</label>
                <select
                  value={sortBy.startsWith('name') ? sortBy : 'name-asc'}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="filter-select"
                >
                  <option value="name-asc">A to Z</option>
                  <option value="name-desc">Z to A</option>
                </select>
              </div>

              <div className="filter-group">
                <label className="filter-label">Sort by Date</label>
                <select
                  value={sortBy.startsWith('date') ? sortBy : 'date-latest'}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="filter-select"
                >
                  <option value="date-latest">Latest First</option>
                  <option value="date-earliest">Earliest First</option>
                </select>
              </div>

              <div className="filter-group">
                <label className="filter-label">Experience</label>
                <select
                  value={experienceFilter}
                  onChange={(e) => setExperienceFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Experience</option>
                  <option value="0-2">0-2 years</option>
                  <option value="2-5">2-5 years</option>
                  <option value="5-10">5-10 years</option>
                  <option value="10+">10+ years</option>
                </select>
              </div>

              <div className="filter-group">
                <label className="filter-label">Date Added</label>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="filter-select"
                >
                  <option value="all">All Dates</option>
                  <option value="today">Today</option>
                  <option value="week">Last Week</option>
                  <option value="month">Last Month</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
            </div>

            {/* Custom Date Range */}
            {dateFilter === 'custom' && (
              <div className="filter-row custom-date-row">
                <div className="filter-group">
                  <label className="filter-label">From Date</label>
                  <input
                    type="date"
                    value={customDateFrom}
                    onChange={(e) => setCustomDateFrom(e.target.value)}
                    className="filter-date-input"
                  />
                </div>
                <div className="filter-group">
                  <label className="filter-label">To Date</label>
                  <input
                    type="date"
                    value={customDateTo}
                    onChange={(e) => setCustomDateTo(e.target.value)}
                    className="filter-date-input"
                  />
                </div>
              </div>
            )}

            {/* Filter Actions */}
            <div className="filter-actions">
              <button
                className="btn btn-secondary"
                onClick={() => {
                  setSortBy('name-asc');
                  setExperienceFilter('all');
                  setDateFilter('all');
                  setCustomDateFrom('');
                  setCustomDateTo('');
                }}
              >
                Reset Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Filter Context Display */}
      {searchResults.length > 0 && getFilterContext().length > 0 && (
        <div className="filter-context">
          <div className="filter-context-header">
            <span className="context-label">Active Filters:</span>
            <span className="filter-mode-indicator">
              ({multipleFilters ? 'Multiple' : 'Single'} Filter Mode)
            </span>
          </div>
          <div className="filter-context-tags">
            {getFilterContext().map((context, index) => (
              <span key={index} className="filter-tag">
                {context}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Results Section */}
      {searchResults.length > 0 ? (
        filteredResults.length > 0 ? (
          <div className="results-section">
            <div className="results-header">
              <h2>Search Results</h2>
              <div className="results-actions">
                <button
                  className="btn btn-secondary"
                  onClick={handleExportAll}
                  title={`Export ${filteredResults.length} results to CSV`}
                >
                  <FiDownload size={16} />
                  Export All ({filteredResults.length})
                </button>
              </div>
            </div>

          <div className="results-grid">
            {filteredResults.map((resume) => (
              <div key={resume.id} className="resume-card">
                <div className="resume-header">
                  <div className="resume-avatar">
                    <FiUser size={24} />
                  </div>
                  <div className="resume-info">
                    <h3 className="resume-name">{formatName(resume.name)}</h3>
                    <p className="resume-title">{formatJobTitle(resume.title)}</p>
                  </div>
                  <button
                    className={`favorite-btn ${favorites[resume.id] ? 'favorited' : ''}`}
                    onClick={() => toggleFavorite(resume)}
                    title={favorites[resume.id] ? 'Remove from favorites' : 'Add to favorites'}
                  >
                    <FiHeart size={16} />
                  </button>
                </div>

                <div className="resume-details">
                  <div className="detail-item location-item">
                    <FiMapPin size={14} />
                    <span>{resume.location}</span>
                  </div>
                  <div
                    className="detail-item email-item clickable"
                    onClick={() => handleEmailClick(resume.email, resume.name)}
                    title={`Send email to ${resume.name}`}
                  >
                    <FiMail size={14} />
                    <span>{resume.email}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                  <div
                    className="detail-item phone-item clickable"
                    onClick={() => handlePhoneClick(resume.phone, resume.name)}
                    title={`Call ${resume.name}`}
                  >
                    <FiPhone size={14} />
                    <span>{formatPhoneNumber(resume.phone)}</span>
                    <FiExternalLink size={12} className="external-icon" />
                  </div>
                </div>

                <div className="resume-content">
                  <div className="content-row">
                    <div className="content-section">
                      <h4>Experience</h4>
                      <p>{resume.experience}</p>
                    </div>
                    <div className="content-section">
                      <h4>Education</h4>
                      <p>{resume.education}</p>
                    </div>
                  </div>
                  <div className="content-section">
                    <h4>Skills</h4>
                    <div className="skills-list">
                      {resume.skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>

                  {/* Added Date Section */}
                  <div className="content-section">
                    <h4>Added</h4>
                    <div className="added-date-container">
                      <FiCalendar size={12} />
                      <span className="added-date">{resume.addedDate}</span>
                    </div>
                  </div>
                </div>

                <div className="resume-actions">
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleView(resume)}
                  >
                    <FiEye size={16} />
                    View Details
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleDownload(resume)}
                  >
                    <FiDownload size={16} />
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination-section">
              <div className="pagination-info">
                Showing {((currentPage - 1) * resultsPerPage) + 1} to {Math.min(currentPage * resultsPerPage, totalCount)} of {totalCount} results
              </div>
              <div className="pagination-controls">
                <button
                  className="btn btn-secondary pagination-btn"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>

                <div className="page-numbers">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        className={`btn pagination-btn ${currentPage === pageNum ? 'btn-primary' : 'btn-secondary'}`}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  className="btn btn-secondary pagination-btn"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
        ) : (
          <div className="no-results-state">
            <div className="empty-icon">
              <FiSearch size={48} />
            </div>
            <h3>No Results Found</h3>
            <p>No resumes match your current filter criteria.</p>
            <div className="search-suggestions">
              <h4>Try:</h4>
              <ul>
                <li>Removing some filter terms</li>
                <li>Using different keywords</li>
                <li>Checking your spelling</li>
                <li>Using more general terms</li>
              </ul>
            </div>
          </div>
        )
      ) : searchResults.length === 0 && hasSearched && !isSearching && (searchQuery.trim() === lastSearchQuery) ? (
        <div className="no-results-state">
          <div className="empty-icon">
            <FiSearch size={48} />
          </div>
          <h3>No Matches Found</h3>
          <p>No resumes match your search for "<strong>{lastSearchQuery}</strong>".</p>
          <div className="search-suggestions">
            <h4>Try:</h4>
            <ul>
              <li>Using different keywords</li>
              <li>Checking your spelling</li>
              <li>Using more general terms</li>
              <li>Searching for skills, job titles, or locations</li>
            </ul>
          </div>
        </div>
      ) : !isSearching && (!hasSearched || searchQuery.trim() !== lastSearchQuery) ? (
        <div className="empty-state">
          <div className="empty-icon">
            <FiSearch size={48} />
          </div>
          <h3>Ready to Search!</h3>
          <p>Use the search box above to find resumes using natural language, or click "Show All" to view all resumes in the database.</p>
          <div className="search-examples">
            <h4>Try searching for:</h4>
            <ul>
              <li>"Find Python developers"</li>
              <li>"Show teachers with 5+ years experience"</li>
              <li>"Software engineers in Mumbai"</li>
            </ul>
          </div>
        </div>
      ) : null}

      {/* Debug Section */}
      {debugInfo && (
        <div className="debug-section">
          <div className="debug-header">
            <h3>Debug Information</h3>
            <button
              className="btn btn-tertiary debug-toggle"
              onClick={() => setShowDebug(!showDebug)}
            >
              {showDebug ? 'Hide Debug' : 'Show Debug'}
            </button>
          </div>

          {showDebug && (
            <div className="debug-content">
              <div className="debug-grid">
                <div className="debug-card">
                  <h4>Search Query</h4>
                  <div className="debug-info">
                    <p><strong>Query:</strong> {debugInfo.query}</p>
                    <p><strong>Match Type:</strong> {debugInfo.match_type}</p>
                    <p><strong>Distance Threshold:</strong> {debugInfo.distance_threshold}</p>
                    <p><strong>Search Method:</strong> {debugInfo.search_method}</p>
                    <p><strong>API Status:</strong> {debugInfo.api_status}</p>
                  </div>
                </div>

                <div className="debug-card">
                  <h4>ChromaDB Query</h4>
                  <div className="debug-code">
                    <pre>{JSON.stringify(debugInfo.debug_info?.chromadb_query || {}, null, 2)}</pre>
                  </div>
                </div>

                <div className="debug-card">
                  <h4>MongoDB Query</h4>
                  <div className="debug-code">
                    <pre>{JSON.stringify(debugInfo.mongo_query || {}, null, 2)}</pre>
                  </div>
                </div>

                <div className="debug-card">
                  <h4>Results Summary</h4>
                  <div className="debug-info">
                    <p><strong>ChromaDB Results:</strong> {debugInfo.debug_info?.total_chromadb_results || 0}</p>
                    <p><strong>MongoDB IDs Found:</strong> {debugInfo.debug_info?.mongodb_ids_found || 0}</p>
                    <p><strong>Final Results:</strong> {debugInfo.results_count}</p>
                    <p><strong>GPT Response:</strong> {debugInfo.gpt_response}</p>
                    <p><strong>Timestamp:</strong> {new Date(debugInfo.timestamp).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Resume Details Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
};

export default SearchResumes;
