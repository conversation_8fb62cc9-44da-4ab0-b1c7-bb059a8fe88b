{"id": "296d65c3-efeb-4c87-8c23-b92f88753e38", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\n  \"Resume\": {\n    \"PersonalInformation\": {\n      \"FullName\": \"Kalpana Vyas\",\n      \"FullNameEmbedding\": null,\n      \"Gender\": \"Female\",\n      \"BirthDate\": \"1979-01-12\",\n      \"Email\": \"<EMAIL>\",\n      \"Address\": \"37/1, <PERSON><PERSON>, <PERSON><PERSON>, Indore, Madhya Pradesh, 452001\",\n      \"City\": \"Indore\",\n      \"State\": \"Madhya Pradesh\",\n      \"ContactNumber\": \"+91 8602193009\",\n      \"LinkedInProfile\": \"\"\n    },\n    \"Objective\": \"To be associated with a company, where I can utilize my skills, gain further experience and excellence in my technical skills while enhancing the company's productivity and reputation.\",\n    \"Education\": [\n      {\n        \"Degree\": \"MA (Master of Arts)\",\n        \"Specialization\": \"Yogic Science\",\n        \"Institution\": \"BRAUSS Mhow\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": null,\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"MA (Master of Arts)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"GDC College, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2002-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"BA (Bachelor of Arts)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"GDC College, Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2000-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"DCA (Diploma in Computer Applications)\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Arihant Institute Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"2006-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"12th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Govt. School No.5 Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"1997-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      },\n      {\n        \"Degree\": \"10th Pass\",\n        \"Specialization\": \"\",\n        \"Institution\": \"Govt. School No.5 Indore\",\n        \"InstitutionEmbedding\": null,\n        \"GraduationYear\": \"1995-12-31\",\n        \"GPA\": 0,\n        \"MaxGPA\": 0,\n        \"Percentage\": 0\n      }\n    ],\n    \"WorkExperience\": [\n      {\n        \"CompanyName\": \"Pink Flower School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Yoga Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": \"2022-06-01\",\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"Working as a yoga teacher since June 2022.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"The International School of Bombay\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Yoga Teacher and Transport Coordinator\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"4 years experience as a yoga teacher and transport coordinator.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Shree Maharshi Vidhya Mandir School\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Yoga and Sports Teacher\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"1 year experience as a yoga and sports teacher in Indore.\",\n        \"Description/ResponsibilityEmbedding\": null\n      },\n      {\n        \"CompanyName\": \"Personal Yoga Classes\",\n        \"CompanyNameEmbedding\": null,\n        \"Role\": \"Yoga Instructor\",\n        \"RoleEmbedding\": null,\n        \"StartYear\": null,\n        \"EndYear\": null,\n        \"Description/Responsibility\": \"2 years experience providing personal yoga classes.\",\n        \"Description/ResponsibilityEmbedding\": null\n      }\n    ],\n    \"TotalWorkExperienceInYears\": 7,\n    \"Skills\": [\n      \"Basic Computer Knowledge\",\n      \"MS-Word\",\n      \"MS-Excel\",\n      \"MS-PowerPoint\"\n    ],\n    \"SkillsEmbedding\": null,\n    \"Certifications\": [\n      {\n        \"CertificationName\": \"Assistant Yoga Teacher Training Certificate\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2016-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Yoga Teacher Training Certificate\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": \"2017-01-01\",\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Pradhan Mantri Kaushal Vikas Yojana Certificate\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"Government of India\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      },\n      {\n        \"CertificationName\": \"Certified Course in Yoga Sciences\",\n        \"CertificationNameEmbedding\": null,\n        \"IssuingOrganization\": \"DAVV\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null,\n        \"ExpiryDate\": null\n      }\n    ],\n    \"Achievements\": [\n      {\n        \"AchievementName\": \"Participation in International Day of Yoga\",\n        \"AchievementNameEmbedding\": null,\n        \"IssuingOrganization\": \"Ministry of Ayush, Government of India\",\n        \"IssuingOrganizationEmbedding\": null,\n        \"IssueDate\": null\n      }\n    ],\n    \"Languages\": [],\n    \"Projects\": []\n  }\n}", "refusal": null, "role": "assistant", "annotations": null, "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "reasoning_content": "First, I am considering the UserContent provided, which is in a CSV-like structure with page numbers, text, and bounding box coordinates. There are tables specified as well. My task is to extract structured data from this resume text and output it in the specified JSON format.\n\nStep 1: Analyze the UserContent. It's unstructured text with coordinates, and there are tables defined. I need to reconstruct the layout using the bounding box coordinates to understand the relative positions and group related information correctly.\n\nStep 2: Reconstruct the exact layout. The coordinates (X1, Y1, X2, Y2) represent bounding boxes. Typically, in PDFs, X increases to the right, and Y increases downwards or upwards depending on the coordinate system. From the values, Y seems to increase downwards since higher Y values are lower on the page (e.g., header at low Y, footer at high Y). I need to use these to infer sections and groupings.\n\nLet me sketch a rough layout based on the coordinates:\n\n- Page 1:\n  - \"Curriculum-Vitae\" at top.\n  - Address, Contact, Email around Y=0.187 to 0.251.\n  - Name \"Kalpana Vyas\" at Y=0.2198 to 0.24432.\n  - Objective from Y=0.27266 to 0.35221.\n  - Educational Qualification mentioned, but details are in tables.\n  - Schooling, Hobbies, etc.\n\n- Page 2:\n  - Technical Skills, Experience, Achievements.\n\n- Page 3:\n  - Date, Place, Name again, and a table with personal details.\n\nThere are tables: Table-1, Table-2 for education, and Table-3 for personal info.\n\nI should use the tables directly as they provide structured data, but I need to cross-verify with the text and use coordinates if needed to resolve ambiguities.\n\nStep 3: Fill out the required output based on the JSON schema. I need to extract all fields specified.\n\nKey sections from schema:\n- PersonalInformation: FullName, Gender, BirthDate, Email, Address, City, State, ContactNumber, LinkedInProfile.\n- Objective\n- Education: Array in highest to lowest order.\n- WorkExperience: Array in order (latest first or based on role hierarchy).\n- TotalWorkExperienceInYears\n- Skills\n- Certifications\n- Achievements\n- Languages (not mentioned, so probably null or empty)\n- Projects (not mentioned, so null or empty)\n\nEmbeddings should be left as null.\n\nStep 4: Ensure no double-counting, handle typos. For example, \"Kalpana Vyas\" is mentioned multiple times, use the most appropriate one. Infer gender from name if not stated.\n\nStep 5: If not specified, set to null or empty as per schema.\n\nNow, extracting data:\n\n- FullName: \"Kalpana Vyas\" is mentioned in text and Table-3. Use \"Kalpana Vyas\".\n\n- Gender: Not explicitly stated, but from name \"Kalpana\" which is typically female, and Table-3 says \"Sex: Female\". So, \"Female\".\n\n- BirthDate: From Table-3, \"DOB: 12/01/1979\". Format it as \"1979-01-12\" for date format.\n\n- Email: From text, \"E-mail Id: <EMAIL>\".\n\n- Address: From text and Table-3, \"Address: 37/1, Raoji Bazar, Juni Indore, Indore,\" and \"(M.P.) 452001\". Also in Table-3 as \"Permanent Address: 37/1, Raoji Bazar, Juni Indore, Indore (MP)\". Use \"37/1, Raoji Bazar, Juni Indore, Indore, Madhya Pradesh, 452001\".\n\n- City: \"Indore\" from address.\n\n- State: \"Madhya Pradesh\" from \"(M.P.)\" and Table-3.\n\n- ContactNumber: \"+91 8602193009\" from text and Table-3.\n\n- LinkedInProfile: Not mentioned, so null or empty string? Schema says type string, but if not found, should be ''. Per step 3, if not found, keep as ''.\n\nSchema for LinkedInProfile is type string, so I should set it to an empty string if not found.\n\nNow, Objective: From page 1, \"To be associated with a company, where I can utilize my skills, gain further experience and, excellence in my technical skills while enhancing the company's productivity and reputation.\" Combine the lines.\n\nEducation: There are tables.\n\nTable-1:\n- M.A., Yogic Science, BRAUSS Mhow, Running\n- M.A., GDC College, Indore, DAVV Indore, 2000-2002\n- B.A., GDC College, Indore, DAVV Indore, 1997-2000\n- DCA, Arihant Institute Indore, Indore, 2006\n\nTable-2:\n- Higher Secondary School, Govt. School No.5 Indore, M.P. Board, 1997\n- High School, Govt. School No.5 Indore, M.P. Board, 1995\n\nNeed to map to schema degrees. Order should be highest to lowest: PhD > Masters > Bachelors > etc.\n\n- M.A. is Master of Arts, should be \"MA (Master of Arts)\" but schema has enum, I can use \"MA\" or match closely. Schema has \"MA (Master of Arts)\", so use that.\n\nSchema enum includes \"MA (Master of Arts)\", etc.\n\nFor \"M.A., Yogic Science\" – Degree: \"MA (Master of Arts)\", Specialization: \"Yogic Science\"\n\nInstitution: \"BRAUSS Mhow\" – probably a typo, might be \"Barkatullah University\" or something, but use as is or standardize. Schema says \"full, standardized name\", but I don't know the standard name. Perhaps \"BRAUSS Mhow\" is not standard; could be \"Devi Ahilya Vishwavidyalaya\" or something, but better to use what's given. Wait, other entries have \"DAVV Indore\" which is Devi Ahilya Vishwavidyalaya. \"BRAUSS Mhow\" might be a mistake. Mhow is a place, perhaps Barkatullah University. But to be safe, use \"BRAUSS Mhow\" as is, but schema says avoid abbreviations. For DAVV, it's written, so perhaps standardize.\n\nIn Table-1, for second M.A., University is \"DAVV Indore\", which is Devi Ahilya Vishwavidyalaya. Similarly, for B.A. same. For first M.A., \"BRAUSS Mhow\" – perhaps it's a different institution. I'll use \"BRAUSS Mhow\" for now.\n\nBut schema says \"full, standardized name\", so for \"GDC College, Indore\", it might be \"Government Degree College\" or something. But I should use the name as given: \"GDC College, Indore\" for consistency, but schema says \"return 'Sage University' instead of 'SU'\", so try to expand if possible. I don't have knowledge, so use as is.\n\nInstitutionEmbedding should be null.\n\nGraduationYear: For running, perhaps null or current year, but say \"Running\", so perhaps set GraduationYear to null.\n\nGPA and Percentage not mentioned, so need to set GPA and MaxGPA. If not mentioned, perhaps set GPA to null, but schema has type number, no null option? Wait, no, in properties, GraduationYear is [\"string\",\"null\"], GPA is type number, but description says \"If mentioned\", and step 3 says if not found, keep as ''. But for number, perhaps use null or 0? Schema for GPA is type number, not nullable in the type, but in JSON schema, I can use null if not specified.\n\nLooking back: \"GPA\":{\"type\":\"number\",\"description\":\"If mentioned, extract GPA value...\"}, so I think it's optional, but type is number, so if not mentioned, I should not include it or set to null. But in JSON, I can set it to null, as the required array includes it, so I must include the field.\n\nRequired fields for Education item: [\"Degree\",\"Specialization\",\"Institution\",\"InstitutionEmbedding\",\"GraduationYear\",\"GPA\",\"MaxGPA\",\"Percentage\"], all are required, but some can be null.\n\nIn schema, GraduationYear is [\"string\",\"null\"], GPA is type number, but I can use null in JSON. I'll set GPA, MaxGPA, Percentage to null if not mentioned.\n\nSimilarly for Percentage.\n\nNow, degrees:\n- First: M.A. in Yogic Science, Institution: BRAUSS Mhow, University/Board: not specified in table for year, but \"Running\" – so Degree: \"MA (Master of Arts)\", Specialization: \"Yogic Science\", Institution: \"BRAUSS Mhow\", GraduationYear: null (since running), GPA null.\n\n- Second: M.A., GDC College, Indore, DAVV Indore, 2000-2002 – Degree: \"MA (Master of Arts)\", Specialization: perhaps not specified, but from context, could be same or different. Table doesn't specify specialization for this M.A., only for first. Wait, Table-1 has \"M.A., Yogic Science\" for first, second is just \"M.A.\", so Specialization might be different or not specified. I'll set Specialization to '' or infer? Better to leave as '' if not specified.\n\nIn Table-1:\nQUALIFICATION,INSTITUTE,UNIVERSITY/BOARD,YEAR\nM.A.,Yogic Science,BRAUSS Mhow,Running → so Qualification is \"M.A.\", Specialization implied in Qualification column? No, it's \"M.A.,Yogic Science\" might be combined. In CSV, it's \"M.A.,Yogic Science\", so probably Degree includes specialization or separate. But in schema, Degree and Specialization are separate.\n\nI need to parse: for \"M.A.,Yogic Science\", Degree=\"MA (Master of Arts)\", Specialization=\"Yogic Science\".\n\nNext row: \"M.A.,\"GDC College, Indore\"\",DAVV Indore,2000-2002\" – Qualification is \"M.A.\", Institute is \"GDC College, Indore\", University is \"DAVV Indore\", Year \"2000-2002\". Specialization not mentioned, so set to ''.\n\nSimilarly for B.A., specialization not mentioned.\n\nDCA: \"DCA\", which is in schema enum as \"DCA (Diploma in Computer Applications)\".\n\nInstitution for DCA: \"Arihant Institute Indore\", University/Board: \"Indore\", Year: \"2006\".\n\nTable-2:\nHigher Secondary School: probably \"12th Pass\", Institution: \"Govt. School No.5 Indore\", Board: \"M.P. Board\", Year: \"1997\"\n\nHigh School: \"10th Pass\", Institution: \"Govt. School No.5 Indore\", Board: \"M.P. Board\", Year: \"1995\"\n\nOrder: Highest to lowest: MA, BA, DCA, 12th, 10th. But MA running might be last or first? Schema says \"Highest to Lowest order, like PhD, MBA, MSc, BSc, 10th Pass, 12th Pass.\" Probably based on level, not completion. So MA should be first if completed, but one is running.\n\nList in order of level: Masters, Bachelors, Diploma, 12th, 10th.\n\nSpecifically: the running MA might be considered highest if pursuing, but typically list completed degrees first. Schema doesn't specify, but says \"Highest to Lowest order\", so perhaps by degree level.\n\nI'll list:\n1. M.A. from GDC College (completed)\n2. B.A. from GDC College\n3. M.A. running from BRAUSS Mhow (but it's another MA, perhaps same level)\nTwo MAs: one completed in 2000-2002, one running. Probably the running one is a second MA or something. But level is same.\n\nPerhaps order by graduation year or something. But schema says \"Highest to Lowest order, like PhD, MBA, MSc, BSc\", so by degree type.\n\nI'll order by degree level and then by completion.\n\nFirst: MA (Master), even running.\nBut better to list completed first.\n\nSchema example is PhD > Masters > Bachelors, so by level.\n\nList all MAs first, then BA, etc.\n\nBut for GraduationYear, one is \"Running\", so set to null.\n\nFor Institution, standardize: \"GDC College, Indore\" – perhaps \"Government Degree College, Indore\", but use as given.\n\n\"DAVV Indore\" is university, but schema asks for Institution, which is the college.\n\nIn table, INSTITUTE is the college, UNIVERSITY/BOARD is the awarding body.\n\nSchema has only \"Institution\", which probably means the place where studied, so use INSTITUTE column.\n\nFor example, for M.A., Institution: \"GDC College, Indore\" or \"BRAUSS Mhow\".\n\nFor schooling, Institution: \"Govt. School No.5 Indore\".\n\nNow, for Education array:\n\n- Degree: match to schema enum. \"M.A.\" to \"MA (Master of Arts)\", \"B.A.\" to \"BA (Bachelor of Arts)\", \"DCA\" to \"DCA (Diploma in Computer Applications)\", \"Higher Secondary School\" to \"12th Pass\", \"High School\" to \"10th Pass\".\n\nSpecialization: for M.A. Yogic Science, it's given. For others, not, so ''.\n\nGraduationYear: for years like \"2000-2002\", perhaps use end year \"2002\", or keep as string. Schema format is date, so perhaps \"2002-01-01\" or something, but it's a range. Use the year mentioned, e.g., \"2002\" for end year.\n\nSchema says format \"date\", so probably expect a full date or year. I'll use the year as string, e.g., \"2002\" for M.A., but format is date, so perhaps \"2002-12-31\" or just the year in string form.\n\nTo be precise, use ISO format if possible. For \"2000-2002\", set GraduationYear to \"2002\" as string, since it's the completion year.\n\nFor \"Running\", set to null.\n\nGPA, MaxGPA, Percentage not mentioned, so null.\n\nNow, WorkExperience: From page 2 text.\n\n- \"2 year experience in personal Yoga Classes.\" – CompanyName: perhaps \"Personal Yoga Classes\" or not specified, Role: \"Yoga Instructor\" or something. Need to fit schema.\n\nSchema has CompanyName, Role, etc.\n\n- \"1 year experience in as a Yoga and Sports teacher at Shree MaharshiVidhyaMandir school, Indore.\" – CompanyName: \"Shree Maharshi Vidhya Mandir School\", Role: \"Yoga and Sports Teacher\"\n\n- \"4 years experience in as a yoga teacher and transport co-ordinator at The Intenational School Of Bombay, bypass\" – probably \"International School of Bombay\", Role: \"Yoga Teacher and Transport Coordinator\"\n\n- \"Work at pink flower school nanda nager as yoga teacher since june 2022.\" – CompanyName: \"Pink Flower School\", Role: \"Yoga Teacher\", StartYear: \"2022-06-01\" or something.\n\nOrder: latest first. The last one started in 2022, so probably current. Others have years but not specified when.\n\nFrom text: \"since june 2022\" for Pink Flower School.\n\nOthers: \"2 year experience\" – no dates, \"1 year\" no dates, \"4 years\" no dates.\n\nBut from education, she graduated BA in 2000, so work experience might be after.\n\nTotalWorkExperienceInYears: sum up, but approximate. 2 + 1 + 4 + current since 2022. But years not specified start/end.\n\nFrom text: \"Work at pink flower school ... since june 2022\" – ongoing.\n\nOthers might be past. Perhaps calculate based on mentioned years.\n\nList them with available dates.\n\nFor StartYear and EndYear, set if available.\n\nFor Pink Flower School: StartYear: \"2022-06-01\", EndYear: null (current)\n\nFor others, no specific years, only duration. Perhaps infer or leave null.\n\nBut in description, it's given.\n\nRole: must include expertise, e.g., \"Yoga Teacher\", \"Yoga and Sports Teacher\".\n\nOrder by latest first: Pink Flower School (2022-present), then perhaps the others, but dates not known. Use the order in text or assume.\n\nText order: personal yoga, then school, then international school, then pink flower. But pink flower is latest.\n\nProbably list Pink Flower first, then International School (assuming earlier), etc.\n\nTotalWorkExperienceInYears: sum of experiences. 2 + 1 + 4 = 7 years, plus from 2022 to now, but since now is not specified, perhaps just sum the mentioned: but one is ongoing. Schema might expect a number, perhaps calculate based on years mentioned. From text, total experience inferred as sum of durations where end year is past.\n\nBut \"4 years experience in ... at The International School\", if ended before 2022, but not specified. Perhaps set TotalWorkExperienceInYears to sum: 2+1+4=7, and current is additional, but since ongoing, maybe include partial. But better to use mentioned or infer.\n\nPerhaps from the experiences, total is at least 7 years plus current.\n\nBut to be precise, I'll calculate from the durations: personal yoga 2 years, yoga and sports teacher 1 year, yoga teacher and coordinator 4 years, current yoga teacher since 2022. Assuming no overlap, total around 7 years past + current. But for simplicity, perhaps set to 7 or 8. Wait, better to sum the completed ones.\n\nSchema says \"Total work experience in years\", so perhaps extract from text if mentioned, else calculate.\n\nNot explicitly mentioned, so sum the years: 2 + 1 + 4 = 7 years for past roles, and current role started 2022, but duration not full year if current year is say 2023. Since resume might be recent, perhaps assume.\n\nI'll set it to 7 for now, or perhaps 8 if including current approximate. But better to use exact if possible. Since one is \"since june 2022\", and DOB is 1979, she is around 44 in 2023, started working after education in 2000, so long career. But I'll sum the mentioned durations for completed roles.\n\nIn WorkExperience array, for each, set Start and End if available.\n\nFor personal yoga: no dates, duration 2 years.\n\nSimilarly for others.\n\nPerhaps set StartYear and EndYear as null if not specified.\n\nFor order, use latest first based on start year or description.\n\nSkills: From page 2, \"TECHNICAL SKILLS : Basic Computer Knowledge. MS-Word, MS-Excel and MS-Power Point.\" So skills: \"Basic Computer Knowledge\", \"MS-Word\", \"MS-Excel\", \"MS-PowerPoint\". But schema is array of strings, so list them.\n\nAlso, from context, yoga related, but not listed.\n\nHobbies include reading news, meditation, listening songs, but that's not skills.\n\nSkills: [\"Basic Computer Knowledge\", \"MS-Word\", \"MS-Excel\", \"MS-PowerPoint\"]\n\nCertifications: From achievements section.\n\n\"Achievements:- Assistant Yoga teacher training Certificate in 2016, Yog teacher training Certificate in 2017, Pradhan kaushal Vikas Yojna (PMKVY) Certificate, Has Participated in International Day of Yoga, Certified course from DAVV in Yoga Sciences., Yoga Camp at BSF, Indore., Member of Malwanchal Yogasan Sports Association\"\n\nSome are certificates, some are participations.\n\nSchema for Certifications: CertificationName, IssuingOrganization, IssueDate, ExpiryDate.\n\nSo:\n- Assistant Yoga teacher training Certificate, 2016 – Name: \"Assistant Yoga Teacher Training Certificate\", Organization: not specified, Date: \"2016-01-01\" or something.\n- Yog teacher training Certificate, 2017 – \"Yoga Teacher Training Certificate\", Organization not specified.\n- Pradhan kaushal Vikas Yojna (PMKVY) Certificate – \"Pradhan Mantri Kaushal Vikas Yojana Certificate\", Organization: perhaps Government of India, Date not specified.\n- Participated in International Day of Yoga – might not be a certification, more achievement.\n- Certified course from DAVV in Yoga Sciences. – \"Certified Course in Yoga Sciences\", IssuingOrganization: \"DAVV\", Date not specified.\n- Yoga Camp at BSF – participation, not certificate.\n- Member of association – not certificate.\n\nSo, extract as certifications: \n1. Assistant Yoga Teacher Training Certificate, IssueDate: \"2016\"\n2. Yoga Teacher Training Certificate, IssueDate: \"2017\"\n3. Pradhan Mantri Kaushal Vikas Yojana Certificate, IssueDate null\n4. Certified Course in Yoga Sciences from DAVV, IssueDate null\n\nFor Organization, infer or leave as ''.\n\nAchievements: Similar, some overlap. \"Has Participated in International Day of Yoga on 21th June Supported by Ministry of Ayush\", \"Yoga Camp at BSF\", \"Member of Malwanchal Yogasan Sports Association\"\n\nSo, Achievements array:\n- AchievementName: \"Participation in International Day of Yoga\", IssuingOrganization: \"Ministry of Ayush, Government of India\", IssueDate: \"June 21\" each year, but specific date not given, perhaps set to null or approximate.\n- \"Yoga Camp at BSF, Indore\" – perhaps not an achievement with issuing org.\n- Membership might not fit.\n\nList what's clearly achievements.\n\nLanguages: Not mentioned in resume, so empty array or null? Schema is array, so [].\n\nProjects: Not mentioned, so [].\n\nNow, for WorkExperience, list:\n1. Current: Pink Flower School, Role: \"Yoga Teacher\", StartYear: \"2022-06-01\", EndYear: null\n2. The International School of Bombay, Role: \"Yoga Teacher and Transport Coordinator\", duration 4 years, no dates\n3. Shree Maharshi Vidhya Mandir School, Role: \"Yoga and Sports Teacher\", duration 1 year\n4. Personal Yoga Classes, Role: \"Yoga Instructor\", duration 2 years\n\nOrder latest first: Pink Flower (2022-), then perhaps International School, but no dates, so based on text order or assume International School is earlier.\n\nText mentions: \"2 year experience in personal Yoga Classes.\" then \"1 year experience in as a Yoga and Sports teacher...\" then \"4 years experience in as a yoga teacher...\" then \"Work at pink flower...\"\n\nProbably personal yoga was first, then schools.\n\nBut latest is Pink Flower.\n\nI'll order by start year, with Pink Flower first.\n\nFor TotalWorkExperienceInYears, sum of durations: 2 + 1 + 4 = 7 years, plus current since 2022. If resume is current, say 1-2 years, but set to 7 for past, or perhaps 8. Since not precise, perhaps calculate from DOB and education, but better to use mentioned. I'll set to 7.\n\nNow, for Education, list in order:\n- MA (Master of Arts), Specialization: Yogic Science or not, Institution: BRAUSS Mhow, GraduationYear: null (running)\n- MA (Master of Arts), Specialization: '', Institution: GDC College, Indore, GraduationYear: \"2002\" (from 2000-2002)\n- BA (Bachelor of Arts), Specialization: '', Institution: GDC College, Indore, GraduationYear: \"2000\"\n- DCA (Diploma in Computer Applications), Specialization: '', Institution: Arihant Institute Indore, GraduationYear: \"2006\"\n- 12th Pass, Institution: Govt. School No.5 Indore, GraduationYear: \"1997\"\n- 10th Pass, Institution: Govt. School No.5 Indore, GraduationYear: \"1995\"\n\nOrder highest to lowest: Masters first. Two MAs, one running, one completed. Perhaps list completed MA first, then running, or vice versa. I'll list by level and completion.\n\nSchema doesn't specify, so list as per degree type.\n\nPut MA Yogic Science (running) first if considering level, but typically list in chronological or level order. I'll list:\n1. MA (2002)\n2. MA running\n3. BA (2000)\nWait, BA is 1997-2000, MA 2000-2002, DCA 2006.\n\nChronologically: BA 2000, MA 2002, DCA 2006, then schooling.\n\nBut schema says \"Highest to Lowest order\", so by degree level: Masters > Bachelors > Diploma > 12th > 10th.\n\nSo:\n- MA (GDC College, 2002)\n- MA (BRAUSS Mhow, running)\n- BA (GDC College, 2000)\n- DCA (Arihant Institute, 2006)\n- 12th Pass (Govt. School, 1997)\n- 10th Pass (Govt. School, 1995)\n\nNote that BA graduation year 2000, MA 2002, but running MA might be after.\n\nFor embeddings, all null.\n\nObjective: Combine the text.\n\nSkills: From technical skills.\n\nLanguages: Not mentioned, so [].\n\nNow, compile into JSON.\n\nFirst, PersonalInformation:\n- FullName: \"Kalpana Vyas\"\n- FullNameEmbedding: null\n- Gender: \"Female\"\n- BirthDate: \"1979-01-12\" (from DOB 12/01/1979, assuming DD/MM/YYYY)\n- Email: \"<EMAIL>\"\n- Address: \"37/1, Raoji Bazar, Juni Indore, Indore, Madhya Pradesh, 452001\"\n- City: \"Indore\"\n- State: \"Madhya Pradesh\" – check schema enum, it has \"Madhya Pradesh\", yes.\n- ContactNumber: \"+91 8602193009\"\n- LinkedInProfile: \"\"\n\nObjective: \"To be associated with a company, where I can utilize my skills, gain further experience and excellence in my technical skills while enhancing the company's productivity and reputation.\"\n\nEducation: array of objects.\n\nWorkExperience: array.\n\nTotalWorkExperienceInYears: let's say 7 (sum of mentioned past experiences)\n\nSkills: [\"Basic Computer Knowledge\", \"MS-Word\", \"MS-Excel\", \"MS-PowerPoint\"] – but perhaps refine to standard, but keep as is.\n\nCertifications: based on achievements section.\n\nAchievements: some from achievements.\n\nLanguages: []\n\nProjects: []\n\nNow, write the JSON."}}], "created": 1750943750, "model": "grok-3-mini-high", "object": "chat.completion", "service_tier": null, "system_fingerprint": "fp_4e23a495a9", "usage": {"completion_tokens": 1366, "prompt_tokens": 5755, "total_tokens": 12746, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 5625, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 4226, "text_tokens": 5755, "image_tokens": 0}, "num_sources_used": 0}}